# Mapping AF Synchronization System

## Problem Analysis

### Original Issues:
1. **Unreliable wait_for_idle()** - Simple polling without confirmation
2. **Race conditions** - AutoFocus starting before movement complete
3. **Status polling conflicts** - Multiple threads checking GRBL status
4. **No movement completion guarantee** - Commands sent but not confirmed

### Root Cause:
GRBL status can be inconsistent due to:
- Serial communication delays
- Buffer management
- Multiple polling sources
- Timing issues between command send and execution

## New Synchronization System

### 1. Process State Flags
```python
# Movement and process flags
self.is_moving = False
self.is_autofocus_running = False
self.movement_timeout = 30.0  # seconds
```

### 2. Robust Movement Completion Check
```python
def _wait_for_movement_complete(self):
    consecutive_idle_count = 0
    required_idle_count = 3  # Require 3 consecutive 'Idle' status
    
    while time.time() - start_time < self.movement_timeout:
        status = self.grbl.get_status()
        
        if status == 'Idle':
            consecutive_idle_count += 1
            if consecutive_idle_count >= required_idle_count:
                time.sleep(0.5)  # Additional stabilization
                return True
        else:
            consecutive_idle_count = 0  # Reset if not idle
        
        time.sleep(0.2)  # Controlled polling interval
    
    return False  # Timeout
```

### 3. Sequential Process Flow
```python
def process_next_point(self):
    # 1. Start movement
    self.is_moving = True
    self.grbl.move_to(x, y)
    
    # 2. Wait for movement completion
    if self._wait_for_movement_complete():
        # 3. Start autofocus only after movement confirmed
        self.is_autofocus_running = True
        self.autofocus_worker.run_autofocus()
    else:
        # Skip point if movement failed
        self.current_point += 1
        self.process_next_point()

@pyqtSlot(float, float)
def on_autofocus_finished(self, best_pos, best_score):
    # 4. Clear autofocus flag
    self.is_autofocus_running = False
    
    # 5. Store result and continue
    self.results[(x, y)] = best_pos
    time.sleep(0.5)  # Stabilization delay
    
    # 6. Move to next point
    self.current_point += 1
    self.process_next_point()
```

### 4. GRBL Polling Management
```python
def run(self):
    # Disable polling during mapping to avoid conflicts
    self.grbl.stop_polling()
    
    # ... mapping process ...
    
    # Re-enable polling when finished
    self.grbl.start_polling()
```

## Key Improvements

### 1. Consecutive Idle Confirmation
- Requires 3 consecutive 'Idle' status readings
- Eliminates false positives from transient status
- Adds stabilization delay after confirmation

### 2. Process State Management
- Clear flags for movement and autofocus states
- Prevents overlapping operations
- Enables proper error handling

### 3. Controlled Timing
- 0.2s intervals for status checking
- 0.5s stabilization delays between operations
- 30s timeout for movement completion

### 4. Conflict Prevention
- Disables GRBL polling during mapping
- Single-threaded status checking
- Sequential operation guarantee

## Expected Behavior

### Movement Phase
```
[MOVEMENT] Waiting for movement completion (timeout: 30s)
[MOVEMENT] Status: Jog
[MOVEMENT] Status: Jog
[MOVEMENT] Status: Idle
[MOVEMENT] Status: Idle
[MOVEMENT] Status: Idle
[MOVEMENT] ✓ Movement confirmed complete after 3 consecutive Idle checks
```

### AutoFocus Phase
```
Starting FULL autofocus at CENTER (36.00, 6.17) - Establishing baseline Z
[AF] Coarse scan: Z=9.000 → Z=10.500
[AF] Fine scan around best position
✓ CENTER AF finished: Z=10.1234, Score=95.67 - BASELINE ESTABLISHED
```

### Complete Sequence
```
1. [MAPPING] Disabling GRBL polling for stable operation
2. Moving to position (36.00, 6.17)
3. [MOVEMENT] ✓ Movement confirmed complete
4. Starting FULL autofocus at CENTER
5. ✓ CENTER AF finished: Z=10.1234 - BASELINE ESTABLISHED
6. Moving to position (32.30, 3.63)
7. [MOVEMENT] ✓ Movement confirmed complete
8. Starting REFINE autofocus
9. ✓ Refine AF finished: Z=10.0987
... (continue for all points)
10. [MAPPING] Re-enabling GRBL polling
11. [MAPPING] Final position: X=39.70, Y=8.71, Z=10.1456
```

## Debugging Features

### Status Logging
- Real-time movement status logging
- Consecutive idle count tracking
- Timeout and error detection

### Process State Tracking
- Clear indication of current operation
- Flag state logging
- Error recovery mechanisms

### Timing Analysis
- Movement completion time tracking
- AutoFocus duration monitoring
- Overall process timing

## Configuration Options

### Timing Parameters
```python
self.movement_timeout = 30.0        # Movement timeout (seconds)
required_idle_count = 3             # Consecutive idle confirmations
status_check_interval = 0.2         # Status polling interval (seconds)
stabilization_delay = 0.5           # Delay between operations (seconds)
```

### Safety Features
- Automatic timeout handling
- Error recovery and continuation
- Process state validation
- Clean shutdown on stop

## Benefits

1. **Reliable Movement Completion** - No more race conditions
2. **Stable AutoFocus Results** - Consistent Z values
3. **Predictable Timing** - Controlled operation sequence
4. **Better Error Handling** - Graceful failure recovery
5. **Clear Debugging** - Detailed process logging
6. **Thread Safety** - No polling conflicts

"""
StitchingUI.py - UI Components untuk Kontrol Stitching
Menyediakan widget dan dialog untuk mengontrol proses stitching
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QPushButton, QSpinBox, QDoubleSpinBox,
                            QProgressBar, QTextEdit, QComboBox, QCheckBox,
                            QDialog, QDialogButtonBox, QFormLayout, QSlider,
                            QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtGui import QPixmap, QFont
import os, time
from configuration import config

class StitchingControlWidget(QWidget):
    """Widget untuk kontrol stitching di UI utama"""
    
    # Signals
    start_stitching_requested = pyqtSignal(str)  # session_name
    stop_stitching_requested = pyqtSignal()
    settings_changed = pyqtSignal(dict)  # settings dict
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.stitching_controller = None
        self.is_stitching = False
        
        self.setup_ui()
        self.setup_connections()
        
        print("StitchingControlWidget: Initialized")
    
    def setup_ui(self):
        """Setup UI components"""
        layout = QVBoxLayout(self)
        
        # Stitching Settings Group
        settings_group = self._create_settings_group()
        layout.addWidget(settings_group)
        
        # Control Buttons Group
        control_group = self._create_control_group()
        layout.addWidget(control_group)
        
        # Progress Group
        progress_group = self._create_progress_group()
        layout.addWidget(progress_group)
        
        # Status Group
        status_group = self._create_status_group()
        layout.addWidget(status_group)
    
    def _create_settings_group(self) -> QGroupBox:
        """Create stitching settings group"""
        group = QGroupBox("Stitching Settings")
        layout = QFormLayout(group)
        
        # Overlap percentage
        self.overlap_spinbox = QSpinBox()
        self.overlap_spinbox.setRange(5, 50)
        self.overlap_spinbox.setValue(int(config.get("stitching.overlap_percentage", 20)))
        self.overlap_spinbox.setSuffix("%")
        layout.addRow("Overlap:", self.overlap_spinbox)
        
        # Capture delay
        self.delay_spinbox = QDoubleSpinBox()
        self.delay_spinbox.setRange(0.1, 10.0)
        self.delay_spinbox.setValue(float(config.get("stitching.capture_delay_s", 1.0)))
        self.delay_spinbox.setSingleStep(0.1)
        self.delay_spinbox.setSuffix("s")
        layout.addRow("Capture Delay:", self.delay_spinbox)
        
        # Stitching method
        self.method_combo = QComboBox()
        self.method_combo.addItems(["Auto", "OpenCV Stitcher", "Manual", "Simple Grid"])
        layout.addRow("Method:", self.method_combo)
        
        # Feature detector
        self.detector_combo = QComboBox()
        self.detector_combo.addItems(["SIFT", "ORB", "AKAZE"])
        layout.addRow("Feature Detector:", self.detector_combo)
        
        return group
    
    def _create_control_group(self) -> QGroupBox:
        """Create control buttons group"""
        group = QGroupBox("Control")
        layout = QHBoxLayout(group)
        
        # Start button
        self.start_button = QPushButton("Start Stitching")
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        layout.addWidget(self.start_button)
        
        # Stop button
        self.stop_button = QPushButton("Stop")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        layout.addWidget(self.stop_button)
        
        # Settings button
        self.settings_button = QPushButton("Advanced Settings")
        layout.addWidget(self.settings_button)
        
        return group
    
    def _create_progress_group(self) -> QGroupBox:
        """Create progress group"""
        group = QGroupBox("Progress")
        layout = QVBoxLayout(group)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        # Grid info label
        self.grid_info_label = QLabel("No ROI selected")
        self.grid_info_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.grid_info_label)
        
        return group
    
    def _create_status_group(self) -> QGroupBox:
        """Create status/log group"""
        group = QGroupBox("Status Log")
        layout = QVBoxLayout(group)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # Clear log button
        clear_button = QPushButton("Clear Log")
        clear_button.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_button)
        
        return group
    
    def setup_connections(self):
        """Setup signal connections"""
        self.start_button.clicked.connect(self._on_start_clicked)
        self.stop_button.clicked.connect(self._on_stop_clicked)
        self.settings_button.clicked.connect(self._on_settings_clicked)
        
        # Settings change signals
        self.overlap_spinbox.valueChanged.connect(self._on_settings_changed)
        self.delay_spinbox.valueChanged.connect(self._on_settings_changed)
        self.method_combo.currentTextChanged.connect(self._on_settings_changed)
        self.detector_combo.currentTextChanged.connect(self._on_settings_changed)
    
    def set_stitching_controller(self, controller):
        """Set stitching controller instance"""
        self.stitching_controller = controller
        
        if controller:
            # Connect controller signals
            controller.stitching_started.connect(self._on_stitching_started)
            controller.stitching_progress.connect(self._on_progress_updated)
            controller.stitching_completed.connect(self._on_stitching_completed)
            controller.stitching_error.connect(self._on_stitching_error)
            
            print("StitchingControlWidget: Controller connected")
    
    def update_grid_info(self, roi_rect):
        """Update grid information display"""
        if not self.stitching_controller or not roi_rect:
            self.grid_info_label.setText("No ROI selected")
            return
        
        try:
            # Get grid calculator dari controller
            if hasattr(self.stitching_controller, 'grid_calculator') and self.stitching_controller.grid_calculator:
                overlap = self.overlap_spinbox.value()
                info = self.stitching_controller.grid_calculator.get_grid_info(roi_rect, overlap)
                
                text = (f"Grid: {info['grid_size'][0]}x{info['grid_size'][1]} "
                       f"({info['total_points']} points) | "
                       f"Est. time: {info['estimated_time_minutes']:.1f} min")
                
                self.grid_info_label.setText(text)
            else:
                self.grid_info_label.setText("Grid calculator not available")
                
        except Exception as e:
            self.grid_info_label.setText(f"Error calculating grid: {e}")
    
    @pyqtSlot()
    def _on_start_clicked(self):
        """Handle start button click"""
        if not self.stitching_controller:
            QMessageBox.warning(self, "Warning", "Stitching controller not available")
            return
        
        # Get session name dari user
        session_name, ok = self._get_session_name()
        if not ok:
            return
        
        # Apply current settings
        self._apply_settings()
        
        # Emit signal
        self.start_stitching_requested.emit(session_name)
    
    @pyqtSlot()
    def _on_stop_clicked(self):
        """Handle stop button click"""
        self.stop_stitching_requested.emit()
    
    @pyqtSlot()
    def _on_settings_clicked(self):
        """Handle settings button click"""
        dialog = AdvancedStitchingDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            settings = dialog.get_settings()
            self.settings_changed.emit(settings)
    
    def _on_settings_changed(self):
        """Handle settings change"""
        if self.stitching_controller:
            self._apply_settings()
    
    def _apply_settings(self):
        """Apply current settings to controller"""
        if not self.stitching_controller:
            return
        
        # Apply overlap
        overlap = self.overlap_spinbox.value()
        self.stitching_controller.set_overlap_percentage(overlap)
        
        # Apply capture delay
        delay = self.delay_spinbox.value()
        self.stitching_controller.set_capture_delay(delay)
        
        # Apply detector type to image stitcher
        if hasattr(self.stitching_controller, 'image_stitcher') and self.stitching_controller.image_stitcher:
            detector = self.detector_combo.currentText()
            self.stitching_controller.image_stitcher.set_detector_type(detector)
    
    def _get_session_name(self) -> tuple:
        """Get session name from user"""
        from PyQt5.QtWidgets import QInputDialog
        
        session_name, ok = QInputDialog.getText(
            self, 
            "Session Name", 
            "Enter session name for stitching:",
            text=f"stitch_{int(time.time())}"
        )
        
        return session_name.strip(), ok
    
    @pyqtSlot()
    def _on_stitching_started(self):
        """Handle stitching started"""
        self.is_stitching = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Stitching started...")
        self.log_text.append("Stitching process started")
    
    @pyqtSlot(int, int, str)
    def _on_progress_updated(self, current, total, status):
        """Handle progress update"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
        
        self.status_label.setText(status)
        self.log_text.append(f"[{current}/{total}] {status}")
        
        # Auto scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    @pyqtSlot(str)
    def _on_stitching_completed(self, result_path):
        """Handle stitching completed"""
        self.is_stitching = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100)
        self.status_label.setText("Stitching completed!")
        self.log_text.append(f"Stitching completed: {result_path}")
        
        # Show completion message
        QMessageBox.information(
            self, 
            "Stitching Completed", 
            f"Stitching completed successfully!\n\nResult saved to:\n{result_path}"
        )
    
    @pyqtSlot(str)
    def _on_stitching_error(self, error_message):
        """Handle stitching error"""
        self.is_stitching = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("Error occurred")
        self.log_text.append(f"ERROR: {error_message}")
        
        # Show error message
        QMessageBox.critical(
            self, 
            "Stitching Error", 
            f"An error occurred during stitching:\n\n{error_message}"
        )

class AdvancedStitchingDialog(QDialog):
    """Dialog untuk advanced stitching settings"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Advanced Stitching Settings")
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup dialog UI"""
        layout = QVBoxLayout(self)
        
        # Form layout untuk settings
        form_layout = QFormLayout()
        
        # Camera FOV settings
        self.fov_width_spinbox = QDoubleSpinBox()
        self.fov_width_spinbox.setRange(0.1, 100.0)
        self.fov_width_spinbox.setValue(5.0)
        self.fov_width_spinbox.setSuffix(" mm")
        form_layout.addRow("Camera FOV Width:", self.fov_width_spinbox)
        
        self.fov_height_spinbox = QDoubleSpinBox()
        self.fov_height_spinbox.setRange(0.1, 100.0)
        self.fov_height_spinbox.setValue(3.75)
        self.fov_height_spinbox.setSuffix(" mm")
        form_layout.addRow("Camera FOV Height:", self.fov_height_spinbox)
        
        # Feature detection settings
        self.sift_features_spinbox = QSpinBox()
        self.sift_features_spinbox.setRange(100, 10000)
        self.sift_features_spinbox.setValue(1000)
        form_layout.addRow("SIFT Features:", self.sift_features_spinbox)
        
        self.match_ratio_spinbox = QDoubleSpinBox()
        self.match_ratio_spinbox.setRange(0.1, 1.0)
        self.match_ratio_spinbox.setValue(0.75)
        self.match_ratio_spinbox.setSingleStep(0.05)
        form_layout.addRow("Match Ratio:", self.match_ratio_spinbox)
        
        # Blending settings
        self.blend_combo = QComboBox()
        self.blend_combo.addItems(["LINEAR", "MULTIBAND"])
        form_layout.addRow("Blend Mode:", self.blend_combo)
        
        layout.addLayout(form_layout)
        
        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_settings(self) -> dict:
        """Get settings dari dialog"""
        return {
            'camera_fov_width': self.fov_width_spinbox.value(),
            'camera_fov_height': self.fov_height_spinbox.value(),
            'sift_features': self.sift_features_spinbox.value(),
            'match_ratio': self.match_ratio_spinbox.value(),
            'blend_mode': self.blend_combo.currentText()
        }

# Test function
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    widget = StitchingControlWidget()
    widget.show()
    
    sys.exit(app.exec_())

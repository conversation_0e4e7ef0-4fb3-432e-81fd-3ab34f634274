import numpy as np
import time

class ZInterpolation:
    """
    Class for interpolating Z values from a 3x3 grid to a 6x6 grid
    using bilinear interpolation for efficient auto-focus mapping.
    """
    
    def __init__(self, grbl_start, grbl_end, mapping_results):
        """
        Initialize Z interpolation with mapping results
        
        Args:
            grbl_start: (x_start, y_start) tuple
            grbl_end: (x_end, y_end) tuple  
            mapping_results: Dictionary with (x,y) keys and z values from MappingAF
        """
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.mapping_results = mapping_results
        
        # Original 3x3 grid points
        self.x_original = np.linspace(grbl_start[0], grbl_end[0], 3)
        self.y_original = np.linspace(grbl_start[1], grbl_end[1], 3)
        
        # New 6x6 grid points (with 0.5 offset)
        self.x_interpolated = np.linspace(grbl_start[0], grbl_end[0], 6)
        self.y_interpolated = np.linspace(grbl_start[1], grbl_end[1], 6)
        
        # Store interpolated results
        self.interpolated_z_grid = None
        self.interpolated_results = {}
        
    def validate_mapping_data(self):
        """
        Validate that we have all 9 points from the mapping
        
        Returns:
            bool: True if all points are valid, False otherwise
        """
        expected_points = []
        for i in range(3):
            for j in range(3):
                x = self.x_original[j]
                y = self.y_original[i]
                expected_points.append((x, y))
        
        missing_points = []
        invalid_z_points = []
        
        for point in expected_points:
            if point not in self.mapping_results:
                missing_points.append(point)
            elif self.mapping_results[point] <= 0:
                invalid_z_points.append(point)
        
        if missing_points:
            print(f"❌ Missing mapping points: {missing_points}")
            return False
            
        if invalid_z_points:
            print(f"⚠️ Invalid Z values (≤0) at points: {invalid_z_points}")
            # We can still interpolate, but warn user
            
        return True
    
    def _bilinear_interpolate(self, x, y):
        """
        Perform bilinear interpolation for a single point using the 3x3 grid

        Args:
            x: X coordinate to interpolate
            y: Y coordinate to interpolate

        Returns:
            float: Interpolated Z value
        """
        # Find the grid cell containing the point
        x_idx = 0
        y_idx = 0

        # Find X index
        for i in range(2):
            if x >= self.x_original[i] and x <= self.x_original[i+1]:
                x_idx = i
                break

        # Find Y index
        for i in range(2):
            if y >= self.y_original[i] and y <= self.y_original[i+1]:
                y_idx = i
                break

        # Get the four corner points
        x1, x2 = self.x_original[x_idx], self.x_original[x_idx+1]
        y1, y2 = self.y_original[y_idx], self.y_original[y_idx+1]

        # Get Z values at corners
        z11 = self.mapping_results.get((x1, y1), 0)
        z12 = self.mapping_results.get((x1, y2), 0)
        z21 = self.mapping_results.get((x2, y1), 0)
        z22 = self.mapping_results.get((x2, y2), 0)

        # Check if any corner has invalid Z
        if any(z <= 0 for z in [z11, z12, z21, z22]):
            # Use nearest neighbor as fallback
            distances = []
            for orig_x in self.x_original:
                for orig_y in self.y_original:
                    z_val = self.mapping_results.get((orig_x, orig_y), 0)
                    if z_val > 0:
                        dist = ((x - orig_x)**2 + (y - orig_y)**2)**0.5
                        distances.append((dist, z_val))

            if distances:
                distances.sort()
                return distances[0][1]  # Return nearest valid Z
            else:
                return 0

        # Perform bilinear interpolation
        # Normalize coordinates to [0,1] within the cell
        if x2 != x1:
            t_x = (x - x1) / (x2 - x1)
        else:
            t_x = 0

        if y2 != y1:
            t_y = (y - y1) / (y2 - y1)
        else:
            t_y = 0

        # Interpolate in X direction
        z_y1 = z11 * (1 - t_x) + z21 * t_x
        z_y2 = z12 * (1 - t_x) + z22 * t_x

        # Interpolate in Y direction
        z_final = z_y1 * (1 - t_y) + z_y2 * t_y

        return z_final

    def create_interpolation_grid(self):
        """
        Create interpolated Z values for 6x6 grid using bilinear interpolation

        Returns:
            bool: True if interpolation successful, False otherwise
        """
        try:
            if not self.validate_mapping_data():
                print("❌ Cannot interpolate: Invalid mapping data")
                return False

            # Check if we have enough valid points
            valid_points = sum(1 for z in self.mapping_results.values() if z > 0)
            if valid_points < 4:
                print(f"❌ Insufficient valid points for interpolation: {valid_points}")
                return False

            # Create 6x6 grid by interpolating each point
            self.interpolated_results = {}

            for i in range(6):
                for j in range(6):
                    x = self.x_interpolated[j]
                    y = self.y_interpolated[i]

                    # Interpolate Z value for this position
                    z = self._bilinear_interpolate(x, y)
                    self.interpolated_results[(x, y)] = z

            # Create 2D array for easier access
            self.interpolated_z_grid = np.zeros((6, 6))
            for i in range(6):
                for j in range(6):
                    x = self.x_interpolated[j]
                    y = self.y_interpolated[i]
                    self.interpolated_z_grid[i, j] = self.interpolated_results[(x, y)]

            print(f"✓ Successfully interpolated Z values for 6x6 grid ({len(self.interpolated_results)} points)")
            return True

        except Exception as e:
            print(f"❌ Error during interpolation: {e}")
            return False
    
    def get_z_at_position(self, x, y):
        """
        Get interpolated Z value at specific X,Y position

        Args:
            x: X coordinate
            y: Y coordinate

        Returns:
            float: Interpolated Z value, or None if position is outside grid
        """
        # Check if position is within bounds
        if (x < self.grbl_start[0] or x > self.grbl_end[0] or
            y < self.grbl_start[1] or y > self.grbl_end[1]):
            print(f"⚠️ Position ({x:.3f}, {y:.3f}) is outside interpolation bounds")
            return None

        try:
            # Use bilinear interpolation directly
            z_value = self._bilinear_interpolate(x, y)
            return float(z_value) if z_value > 0 else None

        except Exception as e:
            print(f"❌ Error interpolating Z at ({x:.3f}, {y:.3f}): {e}")
            return None
    
    def get_interpolated_grid_points(self):
        """
        Get all 6x6 interpolated grid points
        
        Returns:
            dict: Dictionary with (x,y) keys and z values
        """
        return self.interpolated_results.copy()
    
    def log_interpolation_results(self):
        """
        Generate detailed log of interpolation results
        
        Returns:
            str: Formatted log string
        """
        if self.interpolated_z_grid is None:
            return "❌ No interpolation results available"
        
        log = "\n" + "="*60 + "\n"
        log += " " * 20 + "Z INTERPOLATION RESULTS\n"
        log += "="*60 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"Original 3x3 grid -> Interpolated to 6x6 grid\n"
        log += f"ROI Area: X=[{self.grbl_start[0]:.3f}, {self.grbl_end[0]:.3f}], "
        log += f"Y=[{self.grbl_start[1]:.3f}, {self.grbl_end[1]:.3f}]\n"
        log += "-"*60 + "\n"
        
        # Show original 3x3 data
        log += "Original 3x3 Mapping Data:\n"
        for i in range(3):
            row_str = ""
            for j in range(3):
                x = self.x_original[j]
                y = self.y_original[i]
                z = self.mapping_results.get((x, y), 0)
                row_str += f"  Z={z:.4f} |"
            log += row_str[:-2] + "\n"
        
        log += "\n" + "-"*60 + "\n"
        log += "Interpolated 6x6 Grid:\n"
        
        # Show interpolated 6x6 data
        for i in range(6):
            row_str = ""
            for j in range(6):
                x = self.x_interpolated[j]
                y = self.y_interpolated[i]
                z = self.interpolated_results.get((x, y), 0)
                row_str += f" {z:.3f} |"
            log += row_str[:-2] + "\n"
        
        log += "\n" + "-"*60 + "\n"
        log += f"Statistics:\n"
        z_values = [z for z in self.interpolated_results.values() if z > 0]
        if z_values:
            log += f"  Min Z: {min(z_values):.4f}\n"
            log += f"  Max Z: {max(z_values):.4f}\n"
            log += f"  Mean Z: {np.mean(z_values):.4f}\n"
            log += f"  Std Z: {np.std(z_values):.4f}\n"
        
        log += "="*60 + "\n"
        
        return log

    def save_interpolation_data(self, filename=None):
        """
        Save interpolation data to file
        
        Args:
            filename: Optional filename, if None uses timestamp
        """
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"z_interpolation_{timestamp}.txt"
        
        try:
            with open(filename, 'w') as f:
                f.write(self.log_interpolation_results())
            print(f"✓ Interpolation data saved to: {filename}")
        except Exception as e:
            print(f"❌ Error saving interpolation data: {e}")

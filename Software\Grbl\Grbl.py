import serial
import serial.tools.list_ports
import time
from Grbl.value import ValueManager
import json
import re
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QTimer
from pathlib import Path
from configuration import config

class Grbl(QObject):
    position_updated = pyqtSignal(float, float, float, str)

    def __init__(self):
        super().__init__()
        self.grbl = serial.Serial()
        self.grbl.baudrate = config.get("grbl.baudrate", 115200)
        self.grbl.timeout = config.get("grbl.timeout", 1)
        self.is_connected = False
        self.polling_timer = QTimer(self)
        self.polling_timer.timeout.connect(self.poll_status)

    def find_arduino_port(self):
        """Mencari port Arduino secara otomatis"""
        ports = serial.tools.list_ports.comports()
        keywords = [s.lower() for s in config.get("grbl.port_descriptions", ["Arduino", "CH340"])]
        for port in ports:
            desc = (port.description or "").lower()
            if any(k in desc for k in keywords):
                return port.device
        return None

    def connect_grbl(self):
        """Menghubungkan ke GRBL jika port Arduino ditemukan"""
        port = self.find_arduino_port()
        if port:
            try:
                self.grbl.port = port
                self.grbl.open()
                time.sleep(2)  
                self.grbl.write(b"\r\n\r\n")  
                time.sleep(2)
                self.grbl.flushInput()
                print(f"GRBL berhasil terhubung di port: {port}")
                self.start_polling()
            except serial.SerialException as e:
                print("Error saat menghubungkan ke GRBL:", e)
        else:
            print("Arduino tidak ditemukan.")

    def close_connection(self):
        """Menutup koneksi GRBL"""
        self.stop_polling()
        if self.grbl.is_open:
            self.grbl.close()
            print("Koneksi GRBL ditutup.")
    
    def send_command(self, command):
        if self.grbl.is_open:
            self.grbl.write(command.encode() + b"\n")
            
 
    def jog(self, direction, value):
        if self.grbl.is_open:
            valu = ValueManager()
            val = valu.feedrate()
            print(f"Feedrate: {val}")
            code = f"$J=G90 {direction}{value} F{val}"
            print(f"Sending: {code}")
            self.send_command(code)
    
    def stop_jog(self):
        if self.grbl.is_open:
            stop = f"\x85\n"
            self.send_command(stop)
    
    def Home(self):
        if self.grbl.is_open:
            home = f"$H\n"
            self.grbl.write(home.encode())
            print("disini")
    
    def Sample(self):
        if self.grbl.is_open:
            coords = config.get("grbl.sample_coordinates", {"X": 45, "Y": 10, "Z": 9})
            order = ["X", "Y", "Z"]
            parts = []
            for axis in order:
                if axis in coords:
                    parts.append(f"{axis}{coords[axis]}")
            gcode = "G0 " + " ".join(parts) + " \n"
            self.grbl.write(gcode.encode())

    
    def load_settings_from_Json(self, filename):
        # Cari file JSON di folder yang sama dengan Grbl.py
        json_path = Path(__file__).parent / filename
        if not json_path.exists():
            raise FileNotFoundError(f"File {filename} tidak ditemukan di {json_path}")
    
        # Baca JSON dan return dict
        with open(json_path, "r", encoding="utf-8") as f:
            return json.load(f)
    
    def flush_settings_grbl(self, settings_dict):
        for key , value in settings_dict.items():
            if not key.startswith("$"):
                continue
            command = f"{key}={value}"
            if self.grbl.is_open :
                self.send_command(command)
                print(f"Berhasil mengirim setting : {command}")
                time.sleep(0.01)
            else:
                print(f"Gagal mengirim setting: {command}")
    
    def move_to_z(self, Zpos):
        if self.grbl.is_open:
            valu = ValueManager()
            val = valu.feedrate()
            code = f"$J=G90 Z{Zpos} F{val}"
            self.send_command(code)
    
    def move_to(self, X, Y):
        if self.grbl.is_open:
            valu = ValueManager()
            val = valu.feedrate()
            # Use actual feedrate from ValueManager instead of hardcoded F100
            code = f"$J=G90 X{X} Y{Y} F150"
            self.send_command(code)
            print(f"[GRBL] Moving to X{X:.3f} Y{Y:.3f} at F{val} mm/min")
    
    def get_status(self):
        """Mengirim '?' untuk mendapatkan status real-time."""
        if not self.grbl.is_open:
            return "Disconnected"
            
        self.grbl.reset_input_buffer()
        self.grbl.write(b'?')
        status_line = self.grbl.readline().decode('utf-8').strip()

        # Gunakan regular expression untuk mencari status di dalam <...>
        match = re.search(r"<(\w+)", status_line)
        if match:
            self.last_status = match.group(1) # 'Idle', 'Run', 'Jog', dll.
            return self.last_status
        return "Unknown"
    
    def wait_for_idle(self, timeout_seconds=None):
        """
        Menjeda eksekusi dan terus memeriksa status GRBL sampai 'Idle'
        atau hingga timeout tercapai.
        """
        if not self.grbl.is_open:
            print("Tidak bisa menunggu, GRBL tidak terhubung.")
            return

        if timeout_seconds is None:
            timeout_seconds = float(config.get("grbl.wait_idle_timeout_s", 30.0))

        poll_sleep = float(config.get("grbl.poll_sleep_s", 0.1))

        print("Menunggu mesin GRBL menjadi 'Idle'...")
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            status = self.get_status()
            
            if status == 'Idle':
                print("Mesin sudah 'Idle'. Melanjutkan.")
                return True
            
            # Beri jeda singkat agar tidak membanjiri serial port dan CPU
            time.sleep(poll_sleep)  # interval polling dapat diatur melalui konfigurasi

        print(f"TIMEOUT: Mesin tidak menjadi 'Idle' dalam {timeout_seconds} detik.")
        return False
    
    def get_current_z(self):
        """
        Mengambil posisi Z saat ini dari GRBL menggunakan perintah '?'.
        """
        try:
            self.send_command("?")      # Kirim status query
            time.sleep(float(config.get("grbl.poll_sleep_s", 0.1)))             # Beri waktu GRBL merespons
            response = self.read_response()

            # Contoh respons: <Idle|MPos:10.000,5.000,9.200|FS:0,0>
            match = re.search(r"MPos:([-\d.]+),([-\d.]+),([-\d.]+)", response)
            if match:
                x, y, z = map(float, match.groups())
                return x,y,z
        except Exception as e:
            print(f"Error get_current_z: {e}")
        return None
    
    def get_current_position(self):
        """
        qMengambil posisi X, Y, Z saat ini dari GRBL.
        Selalu mengembalikan tuple (float, float, float), bahkan saat error.
        """
        # Langkah 1: Selalu cek koneksi terlebih dahulu
        if not self.grbl.is_open:
            return 0.0, 0.0, 0.0 # Kembalikan nilai default, bukan None

        try:
            # Langkah 2: Bersihkan buffer dan kirim perintah
            self.grbl.reset_input_buffer()
            self.grbl.write(b'?')
            response = self.grbl.readline().decode('utf-8').strip()

            # Langkah 3: Parsing respons
            match = re.search(r"MPos:([-\d.]+),([-\d.]+),([-\d.]+)", response)
            if match:
                # Jika parsing berhasil, konversi ke float dan kembalikan
                x, y, z = map(float, match.groups())
                return x, y, z
            
        except Exception as e:
            print(f"Error saat get_current_position: {e}")
    
        # Langkah 4: Jika terjadi error atau parsing gagal, kembalikan nilai default
        # Ini adalah jaring pengaman untuk mencegah crash
        return 0.0, 0.0, 0.0
    
    def is_machine_idle(self):
        """
        Mengecek apakah mesin dalam keadaan idle atau hold dengan mengirim perintah status GRBL ('?')
        """
        try:
            self.send_command("?")
            time.sleep(float(config.get("grbl.poll_sleep_s", 0.05)))  # Tunggu sejenak agar GRBL merespons
            response = self.read_response()

            # Tambahkan log opsional
            print(f"[GRBL STATUS] {response.strip()}")

            # Cek apakah status mengandung 'Idle' atau 'Hold'
            return "Idle" in response or "Hold" in response
        except Exception as e:
            print(f"Error saat mengecek status GRBL: {e}")
            return False
        
    def start_polling(self, interval_ms=None):
        """Memulai timer untuk memeriksa status GRBL secara berkala."""
        if self.grbl.is_open:
            if interval_ms is None:
                interval_ms = config.get("grbl.status_poll_interval_ms", 250)
            self.polling_timer.start(interval_ms)
            print(f"GRBL polling dimulai dengan interval {interval_ms} ms.")

    def stop_polling(self):
        """Menghentikan timer polling."""
        self.polling_timer.stop()
        print("GRBL polling dihentikan.")
    
    @pyqtSlot()
    def poll_status(self):
        """Dipanggil oleh timer untuk mendapatkan status dan memancarkan sinyal."""
        if not self.grbl.is_open:
            return
            
        status, x, y, z = self.get_status_and_position()
        if status != "Unknown":
            # Pancarkan sinyal dengan data yang didapat
            self.position_updated.emit(x, y, z, status)
    
    def get_status_and_position(self):
        """
        Mengirim '?' ke GRBL untuk mendapatkan status dan posisi (X,Y,Z) saat ini.
        Mengembalikan tuple: (status, x, y, z).
        """
        if not self.grbl.is_open:
            return "Disconnected", 0.0, 0.0, 0.0
            
        try:
            # Kosongkan buffer input untuk memastikan kita mendapatkan balasan yang segar
            self.grbl.reset_input_buffer()
            # Kirim perintah permintaan status '?'
            self.grbl.write(b'?')
            # Baca balasan dari GRBL, contoh: <Idle|MPos:10.500,5.000,2.000|FS:0,0>
            status_line = self.grbl.readline().decode('utf-8').strip()

            # Inisialisasi nilai default
            state = "Unknown"
            x, y, z = 0.0, 0.0, 0.0

            # Gunakan RegEx untuk mencari status (kata di dalam <...>)
            state_match = re.search(r"<(\w+)", status_line)
            if state_match:
                state = state_match.group(1) # Hasilnya: 'Idle', 'Run', 'Jog', dll.

            # Gunakan RegEx untuk mencari posisi mesin (MPos)
            pos_match = re.search(r"MPos:([-\d.]+),([-\d.]+),([-\d.]+)", status_line)
            if pos_match:
                # Konversi string yang ditemukan menjadi float
                x = float(pos_match.group(1))
                y = float(pos_match.group(2))
                z = float(pos_match.group(3))
                
            return state, x, y, z

        except (serial.SerialException, OSError) as e:
            print(f"Error komunikasi serial di get_status_and_position: {e}")
            self.disconnect()
            return "Error", 0.0, 0.0, 0.0


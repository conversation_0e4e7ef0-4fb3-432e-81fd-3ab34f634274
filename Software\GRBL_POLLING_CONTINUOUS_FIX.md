# GRBL Polling Continuous Update Fix

## Problem Description

### Expected Behavior (Before AutoFocus)
- GRBL position label updates continuously every ~250ms
- When GRBL moves manually, position updates in real-time
- Label shows: `X: 36.000  Y: 6.170  Z: 10.094  [Idle]`

### Actual Behavior (After AutoFocus)
- Position updates only once after AutoFocus completes
- Manual GRBL movement does NOT update the position label
- Label becomes "stuck" at last AutoFocus position
- Continuous polling stops working

## Root Cause Analysis

### Thread Movement Issues
```python
# During AutoFocus
self.grbl.moveToThread(self.autofocus_thread)  # GRBL moved to AF thread
# ... AutoFocus runs ...
self.grbl.moveToThread(self.thread())          # GRBL moved back to main thread
```

**Problems:**
1. Moving GRBL between threads disrupts polling timer
2. Timer may not restart properly in main thread
3. Signal connections may be broken
4. Polling state becomes inconsistent

### Insufficient Restart Logic
```python
# Previous (INSUFFICIENT)
if not self.grbl.polling_timer.isActive():
    self.grbl.start_polling()  # Only starts if not active
```

**Issues:**
- Doesn't handle "zombie" timers (active but not working)
- No verification that polling actually works
- No signal reconnection

## Comprehensive Fix Implemented

### 1. Force Stop-Start Polling Cycle
```python
# Force restart GRBL polling untuk update posisi UI
try:
    if hasattr(self.grbl, 'polling_timer'):
        # Stop dulu jika masih aktif
        if self.grbl.polling_timer.isActive():
            print("[UI] Stopping existing GRBL polling")
            self.grbl.stop_polling()
        
        # Tunggu sebentar untuk memastikan stop
        import time
        time.sleep(0.1)
        
        # Start ulang polling
        print("[UI] Force restarting GRBL polling after AutoFocus")
        self.grbl.start_polling()
```

### 2. Verify Polling Success
```python
        # Verify polling is running
        if self.grbl.polling_timer.isActive():
            print("[UI] ✓ GRBL polling successfully restarted")
        else:
            print("[UI] ❌ GRBL polling failed to restart")
```

### 3. Reconnect Position Signal
```python
        # Ensure signal connection is still active
        try:
            self.grbl.position_updated.disconnect()
        except:
            pass  # Ignore if already disconnected
        
        self.grbl.position_updated.connect(self.update_grbl_position_label)
        print("[UI] ✓ GRBL position signal reconnected")
```

### 4. Applied to Both AutoFocus and Mapping AF
- Same fix applied to `on_autofocus_finished()`
- Same fix applied to `on_mapping_af_finished()`
- Consistent behavior across all AF operations

## Expected Behavior After Fix

### During AutoFocus
```
[UI] Starting AutoFocus...
[UI] GRBL polling may be disrupted during AF (normal)
```

### After AutoFocus Completes
```
[UI] Stopping existing GRBL polling
GRBL polling dihentikan.
[UI] Force restarting GRBL polling after AutoFocus
GRBL polling dimulai dengan interval 250 ms.
[UI] ✓ GRBL polling successfully restarted
[UI] ✓ GRBL position signal reconnected
[UI] Position updated after AutoFocus: X=36.000, Y=6.170, Z=10.094 [Idle]
```

### Continuous Updates Resume
```
# Every 250ms automatically:
X: 36.000  Y: 6.170  Z: 10.094  [Idle]
X: 36.000  Y: 6.170  Z: 10.094  [Idle]
X: 36.000  Y: 6.170  Z: 10.094  [Idle]

# When manually moved:
X: 37.500  Y: 7.200  Z: 10.094  [Jog]
X: 38.000  Y: 7.500  Z: 10.094  [Jog]
X: 38.000  Y: 7.500  Z: 10.094  [Idle]
```

## Key Improvements

### 1. Robust Restart Process
- **Force stop**: Ensures clean state
- **Delay**: Allows proper cleanup
- **Force start**: Guarantees fresh timer
- **Verification**: Confirms success

### 2. Signal Reconnection
- **Disconnect first**: Prevents duplicate connections
- **Reconnect**: Ensures signal works
- **Verification**: Logs success

### 3. Comprehensive Logging
- Clear indication of each step
- Success/failure feedback
- Easy debugging

### 4. Consistent Application
- Same fix for AutoFocus and Mapping AF
- Uniform behavior across operations

## Testing Checklist

### Before AutoFocus
- [ ] Position updates continuously every ~250ms
- [ ] Manual movement updates position in real-time
- [ ] Status shows current GRBL state

### After AutoFocus
- [ ] See restart messages in console
- [ ] Position updates resume automatically
- [ ] Manual movement updates position correctly
- [ ] No "stuck" position display

### Verification Steps
1. **Start application** → Position should update continuously
2. **Run AutoFocus** → Position may pause during AF (normal)
3. **AF completes** → Should see restart messages
4. **Move GRBL manually** → Position should update in real-time
5. **Wait without moving** → Position should still update every 250ms

## Console Output to Look For

### Success Pattern
```
[UI] Stopping existing GRBL polling
GRBL polling dihentikan.
[UI] Force restarting GRBL polling after AutoFocus
GRBL polling dimulai dengan interval 250 ms.
[UI] ✓ GRBL polling successfully restarted
[UI] ✓ GRBL position signal reconnected
```

### Failure Pattern (Should Not Happen)
```
[UI] ❌ GRBL polling failed to restart
[UI] ❌ GRBL polling_timer not found
[UI] Error restarting GRBL polling: [error message]
```

## Benefits

1. **Continuous Updates**: Position always shows current state
2. **Real-time Tracking**: Manual movements immediately reflected
3. **Reliable Operation**: Works consistently after any AF operation
4. **Better UX**: Users always know current position
5. **Easy Debugging**: Clear logging for troubleshooting

"""
ImageStitcher.py - Grid-Based Image Stitching dengan Phase Correlation
Menggunakan phase correlation untuk alignment dan multi-band blending untuk seamless stitching
"""

import cv2
import numpy as np
import os
from typing import List, Optional, Tuple
import time

class ImageStitcher:
    def __init__(self):
        """Inisialisasi Grid-Based Image Stitcher"""
        self.blend_mode = "MULTIBAND"  # MULTIBAND atau LINEAR
        self.overlap_percentage = 20   # Default overlap percentage

        # Parameters untuk phase correlation
        self.phase_correlation_threshold = 0.1
        self.subpixel_accuracy = True

        # Parameters untuk multi-band blending
        self.num_bands = 6  # Jumlah frequency bands untuk blending
        self.blend_strength = 1.0

        # Grid parameters
        self.grid_cols = 0
        self.grid_rows = 0

        print("ImageStitcher: Initialized (Grid-Based with Phase Correlation)")
        print(f"Blend mode: {self.blend_mode}, Overlap: {self.overlap_percentage}%")
    
    def set_grid_parameters(self, grid_cols: int, grid_rows: int, overlap_percentage: int):
        """
        Set parameter grid untuk stitching

        Args:
            grid_cols: Jumlah kolom grid
            grid_rows: Jumlah baris grid
            overlap_percentage: Persentase overlap antar gambar
        """
        self.grid_cols = grid_cols
        self.grid_rows = grid_rows
        self.overlap_percentage = overlap_percentage
        print(f"ImageStitcher: Grid set to {grid_cols}x{grid_rows} with {overlap_percentage}% overlap")

    def set_blend_mode(self, blend_mode: str):
        """
        Set mode blending

        Args:
            blend_mode: "LINEAR" atau "MULTIBAND"
        """
        if blend_mode in ["LINEAR", "MULTIBAND"]:
            self.blend_mode = blend_mode
            print(f"ImageStitcher: Blend mode set to {blend_mode}")
        else:
            print(f"ImageStitcher: Invalid blend mode: {blend_mode}")

    def set_phase_correlation_params(self, threshold: float = 0.1, subpixel: bool = True):
        """
        Set parameter phase correlation

        Args:
            threshold: Threshold untuk phase correlation
            subpixel: Enable subpixel accuracy
        """
        self.phase_correlation_threshold = threshold
        self.subpixel_accuracy = subpixel
        print(f"ImageStitcher: Phase correlation - threshold: {threshold}, subpixel: {subpixel}")
    
    def stitch_images(self, image_paths: List[str], output_dir: str, session_name: str) -> Optional[str]:
        """
        Grid-based stitching dengan phase correlation dan multi-band blending

        Args:
            image_paths: List path ke gambar-gambar yang akan di-stitch (dalam urutan grid)
            output_dir: Direktori output
            session_name: Nama session untuk output file

        Returns:
            str: Path ke hasil stitching, None jika gagal
        """
        try:
            print(f"ImageStitcher: Starting grid-based stitching with {len(image_paths)} images")
            start_time = time.time()

            # Load semua gambar
            images = self._load_images(image_paths)
            if not images:
                print("ImageStitcher: No valid images to stitch")
                return None

            print(f"ImageStitcher: Loaded {len(images)} images")

            # Hitung grid size jika belum di-set
            if self.grid_cols == 0 or self.grid_rows == 0:
                self._auto_calculate_grid_size(len(images))

            # Method 1: Grid-based stitching dengan phase correlation
            result_path = self._stitch_grid_based(images, output_dir, session_name)

            if result_path:
                elapsed_time = time.time() - start_time
                print(f"ImageStitcher: Grid-based stitching completed in {elapsed_time:.2f}s")
                return result_path

            # Method 2: Fallback ke simple grid tanpa phase correlation
            print("ImageStitcher: Phase correlation failed, trying simple grid...")
            result_path = self._stitch_simple_grid(images, output_dir, session_name)

            if result_path:
                elapsed_time = time.time() - start_time
                print(f"ImageStitcher: Simple grid stitching completed in {elapsed_time:.2f}s")
                return result_path

            print("ImageStitcher: All stitching methods failed")
            return None

        except Exception as e:
            print(f"ImageStitcher: Error in stitching process: {e}")
            return None
    
    def _load_images(self, image_paths: List[str]) -> List[np.ndarray]:
        """Load gambar dari file paths"""
        images = []
        
        for path in image_paths:
            if not os.path.exists(path):
                print(f"ImageStitcher: Image not found: {path}")
                continue
            
            try:
                img = cv2.imread(path)
                if img is not None:
                    images.append(img)
                else:
                    print(f"ImageStitcher: Failed to load image: {path}")
            except Exception as e:
                print(f"ImageStitcher: Error loading {path}: {e}")
        
        return images

    def _auto_calculate_grid_size(self, num_images: int):
        """Auto calculate grid size berdasarkan jumlah gambar"""
        self.grid_cols = int(np.ceil(np.sqrt(num_images)))
        self.grid_rows = int(np.ceil(num_images / self.grid_cols))
        print(f"ImageStitcher: Auto-calculated grid size: {self.grid_cols}x{self.grid_rows}")

    def _stitch_grid_based(self, images: List[np.ndarray], output_dir: str, session_name: str) -> Optional[str]:
        """
        Grid-based stitching dengan phase correlation dan multi-band blending

        Args:
            images: List gambar dalam urutan grid (row-major order)
            output_dir: Direktori output
            session_name: Nama session

        Returns:
            str: Path hasil stitching atau None jika gagal
        """
        try:
            print("ImageStitcher: Starting grid-based stitching with phase correlation...")

            if len(images) != self.grid_cols * self.grid_rows:
                print(f"ImageStitcher: Warning - Expected {self.grid_cols * self.grid_rows} images, got {len(images)}")

            # Get image dimensions
            h, w = images[0].shape[:2]

            # Hitung overlap dalam pixel
            overlap_x = int(w * self.overlap_percentage / 100)
            overlap_y = int(h * self.overlap_percentage / 100)

            print(f"ImageStitcher: Image size: {w}x{h}, Overlap: {overlap_x}x{overlap_y} pixels")

            # Hitung ukuran canvas final
            canvas_width = self.grid_cols * w - (self.grid_cols - 1) * overlap_x
            canvas_height = self.grid_rows * h - (self.grid_rows - 1) * overlap_y

            print(f"ImageStitcher: Canvas size: {canvas_width}x{canvas_height}")

            # Create canvas
            canvas = np.zeros((canvas_height, canvas_width, 3), dtype=np.float32)
            weight_map = np.zeros((canvas_height, canvas_width), dtype=np.float32)

            # Process setiap gambar dalam grid
            for i, img in enumerate(images):
                if i >= self.grid_cols * self.grid_rows:
                    break

                row = i // self.grid_cols
                col = i % self.grid_cols

                print(f"ImageStitcher: Processing image {i+1}/{len(images)} at grid ({row}, {col})")

                # Hitung posisi nominal
                nominal_x = col * (w - overlap_x)
                nominal_y = row * (h - overlap_y)

                # Phase correlation untuk fine alignment
                actual_x, actual_y = self._phase_correlation_alignment(
                    canvas, img, nominal_x, nominal_y, overlap_x, overlap_y
                )

                # Place image dengan blending
                self._place_image_with_blending(
                    canvas, weight_map, img, actual_x, actual_y, overlap_x, overlap_y
                )

            # Normalize canvas
            mask = weight_map > 0
            canvas[mask] = canvas[mask] / weight_map[mask, np.newaxis]

            # Convert ke uint8
            result = np.clip(canvas, 0, 255).astype(np.uint8)

            # Apply multi-band blending jika enabled
            if self.blend_mode == "MULTIBAND":
                result = self._apply_multiband_blending(result, weight_map)

            # Save result
            output_filename = f"{session_name}_stitched_grid.jpg"
            output_path = os.path.join(output_dir, output_filename)

            success = cv2.imwrite(output_path, result)
            if success:
                print(f"ImageStitcher: Grid-based stitching successful - {output_path}")
                return output_path
            else:
                print("ImageStitcher: Failed to save grid stitched image")
                return None

        except Exception as e:
            print(f"ImageStitcher: Error in grid-based stitching: {e}")
            return None

    def _phase_correlation_alignment(self, canvas: np.ndarray, img: np.ndarray,
                                   nominal_x: int, nominal_y: int,
                                   overlap_x: int, overlap_y: int) -> Tuple[int, int]:
        """
        Gunakan phase correlation untuk fine alignment

        Args:
            canvas: Canvas yang sudah ada
            img: Gambar yang akan di-align
            nominal_x, nominal_y: Posisi nominal
            overlap_x, overlap_y: Ukuran overlap

        Returns:
            Tuple[int, int]: Posisi actual setelah alignment
        """
        try:
            # Jika canvas masih kosong, gunakan posisi nominal
            if np.sum(canvas) == 0:
                return nominal_x, nominal_y

            # Convert ke grayscale untuk phase correlation
            img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if len(img.shape) == 3 else img

            # Extract overlap region dari canvas
            canvas_gray = cv2.cvtColor(canvas.astype(np.uint8), cv2.COLOR_BGR2GRAY) if len(canvas.shape) == 3 else canvas.astype(np.uint8)

            # Define search region (sedikit lebih besar dari overlap)
            search_margin = 20  # pixel
            search_x1 = max(0, nominal_x - search_margin)
            search_y1 = max(0, nominal_y - search_margin)
            search_x2 = min(canvas.shape[1], nominal_x + img.shape[1] + search_margin)
            search_y2 = min(canvas.shape[0], nominal_y + img.shape[0] + search_margin)

            canvas_region = canvas_gray[search_y1:search_y2, search_x1:search_x2]

            if canvas_region.size == 0:
                return nominal_x, nominal_y

            # Phase correlation
            shift = cv2.phaseCorrelate(
                np.float32(img_gray),
                np.float32(canvas_region)
            )

            if self.subpixel_accuracy:
                dx, dy = shift[0]
            else:
                dx, dy = int(round(shift[0][0])), int(round(shift[0][1]))

            # Apply shift dengan batas reasonable
            max_shift = min(overlap_x, overlap_y) // 2
            dx = np.clip(dx, -max_shift, max_shift)
            dy = np.clip(dy, -max_shift, max_shift)

            actual_x = nominal_x + int(dx)
            actual_y = nominal_y + int(dy)

            print(f"ImageStitcher: Phase correlation shift: ({dx:.1f}, {dy:.1f})")

            return actual_x, actual_y

        except Exception as e:
            print(f"ImageStitcher: Phase correlation failed: {e}")
            return nominal_x, nominal_y

    def _place_image_with_blending(self, canvas: np.ndarray, weight_map: np.ndarray,
                                 img: np.ndarray, x: int, y: int,
                                 overlap_x: int, overlap_y: int):
        """
        Place image di canvas dengan weight-based blending

        Args:
            canvas: Canvas target
            weight_map: Weight map untuk blending
            img: Gambar yang akan ditempatkan
            x, y: Posisi placement
            overlap_x, overlap_y: Ukuran overlap
        """
        try:
            h, w = img.shape[:2]

            # Hitung region placement
            x1 = max(0, x)
            y1 = max(0, y)
            x2 = min(canvas.shape[1], x + w)
            y2 = min(canvas.shape[0], y + h)

            # Hitung region dari image
            img_x1 = max(0, -x)
            img_y1 = max(0, -y)
            img_x2 = img_x1 + (x2 - x1)
            img_y2 = img_y1 + (y2 - y1)

            if x2 <= x1 or y2 <= y1:
                return

            # Extract image region
            img_region = img[img_y1:img_y2, img_x1:img_x2].astype(np.float32)

            # Create weight untuk region ini
            weight = self._create_weight_map(img_region.shape[:2], overlap_x, overlap_y)

            # Blend dengan existing canvas
            canvas[y1:y2, x1:x2] += img_region * weight[:, :, np.newaxis]
            weight_map[y1:y2, x1:x2] += weight

        except Exception as e:
            print(f"ImageStitcher: Error placing image: {e}")

    def _create_weight_map(self, shape: Tuple[int, int], overlap_x: int, overlap_y: int) -> np.ndarray:
        """
        Create weight map untuk smooth blending

        Args:
            shape: (height, width) dari region
            overlap_x, overlap_y: Ukuran overlap

        Returns:
            np.ndarray: Weight map
        """
        h, w = shape
        weight = np.ones((h, w), dtype=np.float32)

        # Linear falloff di overlap regions
        if overlap_x > 0:
            # Left edge
            for i in range(min(overlap_x, w)):
                weight[:, i] *= (i + 1) / overlap_x
            # Right edge
            for i in range(min(overlap_x, w)):
                weight[:, w - 1 - i] *= (i + 1) / overlap_x

        if overlap_y > 0:
            # Top edge
            for i in range(min(overlap_y, h)):
                weight[i, :] *= (i + 1) / overlap_y
            # Bottom edge
            for i in range(min(overlap_y, h)):
                weight[h - 1 - i, :] *= (i + 1) / overlap_y

        return weight

    def _apply_multiband_blending(self, image: np.ndarray, weight_map: np.ndarray) -> np.ndarray:
        """
        Apply multi-band blending untuk hasil yang lebih smooth

        Args:
            image: Input image
            weight_map: Weight map dari blending

        Returns:
            np.ndarray: Blended image
        """
        try:
            print("ImageStitcher: Applying multi-band blending...")

            # Convert ke float untuk processing
            img_float = image.astype(np.float32) / 255.0

            # Create Gaussian dan Laplacian pyramids
            gaussian_pyr = self._build_gaussian_pyramid(img_float, self.num_bands)
            laplacian_pyr = self._build_laplacian_pyramid(gaussian_pyr)

            # Apply blending di setiap level pyramid
            blended_pyr = []
            for i, lap_level in enumerate(laplacian_pyr):
                # Scale weight map ke level pyramid ini
                level_weight = cv2.resize(weight_map,
                                        (lap_level.shape[1], lap_level.shape[0]))

                # Normalize weight
                level_weight = level_weight / (np.max(level_weight) + 1e-6)

                # Apply weight ke setiap channel
                if len(lap_level.shape) == 3:
                    level_weight = level_weight[:, :, np.newaxis]

                blended_level = lap_level * level_weight * self.blend_strength
                blended_pyr.append(blended_level)

            # Reconstruct dari pyramid
            result = self._reconstruct_from_laplacian_pyramid(blended_pyr)

            # Convert kembali ke uint8
            result = np.clip(result * 255, 0, 255).astype(np.uint8)

            print("ImageStitcher: Multi-band blending completed")
            return result

        except Exception as e:
            print(f"ImageStitcher: Multi-band blending failed: {e}")
            return image

    def _build_gaussian_pyramid(self, image: np.ndarray, levels: int) -> List[np.ndarray]:
        """Build Gaussian pyramid"""
        pyramid = [image.copy()]

        for i in range(levels - 1):
            image = cv2.pyrDown(image)
            pyramid.append(image)

        return pyramid

    def _build_laplacian_pyramid(self, gaussian_pyr: List[np.ndarray]) -> List[np.ndarray]:
        """Build Laplacian pyramid dari Gaussian pyramid"""
        laplacian_pyr = []

        for i in range(len(gaussian_pyr) - 1):
            # Upscale level berikutnya
            upscaled = cv2.pyrUp(gaussian_pyr[i + 1])

            # Resize jika perlu untuk match dimensions
            if upscaled.shape[:2] != gaussian_pyr[i].shape[:2]:
                upscaled = cv2.resize(upscaled,
                                    (gaussian_pyr[i].shape[1], gaussian_pyr[i].shape[0]))

            # Laplacian = current - upscaled_next
            laplacian = gaussian_pyr[i] - upscaled
            laplacian_pyr.append(laplacian)

        # Add top level
        laplacian_pyr.append(gaussian_pyr[-1])

        return laplacian_pyr

    def _reconstruct_from_laplacian_pyramid(self, laplacian_pyr: List[np.ndarray]) -> np.ndarray:
        """Reconstruct image dari Laplacian pyramid"""
        # Start dari top level
        result = laplacian_pyr[-1].copy()

        # Reconstruct dari atas ke bawah
        for i in range(len(laplacian_pyr) - 2, -1, -1):
            # Upscale current result
            result = cv2.pyrUp(result)

            # Resize jika perlu
            if result.shape[:2] != laplacian_pyr[i].shape[:2]:
                result = cv2.resize(result,
                                  (laplacian_pyr[i].shape[1], laplacian_pyr[i].shape[0]))

            # Add Laplacian level
            result += laplacian_pyr[i]

        return result
    

    
    def _stitch_simple_grid(self, images: List[np.ndarray], output_dir: str, session_name: str) -> Optional[str]:
        """
        Simple grid stitching dengan overlap - fallback method tanpa phase correlation

        Args:
            images: List gambar dalam urutan grid
            output_dir: Direktori output
            session_name: Nama session

        Returns:
            str: Path hasil stitching atau None jika gagal
        """
        try:
            print("ImageStitcher: Trying simple grid stitching with overlap...")

            if not images:
                return None

            # Gunakan grid size yang sudah di-set atau auto-calculate
            if self.grid_cols == 0 or self.grid_rows == 0:
                self._auto_calculate_grid_size(len(images))

            print(f"ImageStitcher: Grid size: {self.grid_cols} x {self.grid_rows}")

            # Get image dimensions
            h, w = images[0].shape[:2]

            # Hitung overlap dalam pixel
            overlap_x = int(w * self.overlap_percentage / 100)
            overlap_y = int(h * self.overlap_percentage / 100)

            # Hitung ukuran canvas final
            canvas_width = self.grid_cols * w - (self.grid_cols - 1) * overlap_x
            canvas_height = self.grid_rows * h - (self.grid_rows - 1) * overlap_y

            print(f"ImageStitcher: Canvas size: {canvas_width}x{canvas_height} with {overlap_x}x{overlap_y} overlap")

            # Create result canvas
            result = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

            # Place images in grid dengan overlap
            for i, img in enumerate(images):
                if i >= self.grid_cols * self.grid_rows:
                    break

                row = i // self.grid_cols
                col = i % self.grid_cols

                # Hitung posisi dengan overlap
                x_start = col * (w - overlap_x)
                y_start = row * (h - overlap_y)
                x_end = x_start + w
                y_end = y_start + h

                # Pastikan tidak keluar dari canvas
                x_end = min(x_end, canvas_width)
                y_end = min(y_end, canvas_height)

                # Resize image jika perlu
                if img.shape[:2] != (h, w):
                    img = cv2.resize(img, (w, h))

                # Extract region yang akan ditempatkan
                img_width = x_end - x_start
                img_height = y_end - y_start

                if img_width > 0 and img_height > 0:
                    img_region = img[:img_height, :img_width]

                    # Simple blending di overlap area
                    existing_region = result[y_start:y_end, x_start:x_end]

                    # Jika ada overlap, blend dengan rata-rata
                    mask = np.any(existing_region > 0, axis=2)
                    if np.any(mask):
                        # Blend area yang overlap
                        blended_region = existing_region.copy()
                        blended_region[mask] = (existing_region[mask].astype(np.float32) +
                                              img_region[mask].astype(np.float32)) / 2
                        blended_region[~mask] = img_region[~mask]
                        result[y_start:y_end, x_start:x_end] = blended_region.astype(np.uint8)
                    else:
                        # Tidak ada overlap, langsung place
                        result[y_start:y_end, x_start:x_end] = img_region

            # Save result
            output_filename = f"{session_name}_stitched_simple.jpg"
            output_path = os.path.join(output_dir, output_filename)

            success = cv2.imwrite(output_path, result)
            if success:
                print(f"ImageStitcher: Simple grid stitching successful - {output_path}")
                return output_path
            else:
                print("ImageStitcher: Failed to save simple grid stitched image")
                return None

        except Exception as e:
            print(f"ImageStitcher: Error in simple grid stitching: {e}")
            return None
    


# Test function
if __name__ == "__main__":
    print("ImageStitcher - Grid-Based Image Stitching dengan Phase Correlation")
    print("Features:")
    print("- Grid-based stitching dengan 20% overlap")
    print("- Phase correlation untuk fine alignment")
    print("- Multi-band blending untuk seamless hasil")
    print("- Simple grid fallback jika phase correlation gagal")
    print("\nGunakan dari StitchingController atau StitchingWorker")

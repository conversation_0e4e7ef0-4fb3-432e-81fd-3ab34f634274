import sys
import os
sys.path.append('Software/Auto_Focus')

from Z_Interpolation import ZInterpolation

# Simple test
grbl_start = (0.0, 0.0)
grbl_end = (3.0, 3.0)

mapping_results = {
    (0.0, 0.0): 10.1,
    (1.5, 0.0): 10.2,
    (3.0, 0.0): 10.1,
    (0.0, 1.5): 10.0,
    (1.5, 1.5): 10.15,
    (3.0, 1.5): 10.2,
    (0.0, 3.0): 10.05,
    (1.5, 3.0): 10.1,
    (3.0, 3.0): 10.15
}

print("Creating Z interpolation...")
z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)

print("Running interpolation...")
if z_interp.create_interpolation_grid():
    print("✓ Interpolation successful!")
    
    # Test a few positions
    test_pos = [(0.6, 0.6), (1.2, 1.8), (2.4, 2.4)]
    for x, y in test_pos:
        z = z_interp.get_z_at_position(x, y)
        print(f"Position ({x}, {y}) -> Z = {z:.4f}")
else:
    print("❌ Interpolation failed!")

# AutoFocus Thread Issues - Fixed

## Problems Identified

### 1. Thread Error After AutoFocus
```
[AF] Emitting focus_finished: Z=10.0943, Score=24.42
QObject::moveToThread: Current thread (0x1c406520f90) is not the object's thread (0x1c427ce5810).
Cannot move to target thread (0x1c406520f90)
```

### 2. GRBL Position UI Stuck After AutoFocus
- Position updates work before AutoFocus
- After AutoFocus completes, position display stuck
- Manual movement still works but position not updated in UI

## Root Cause Analysis

### Thread Management Issues
```python
# In on_autofocus_clicked() and on_refine_focus_clicked()
self.autofocus_worker.moveToThread(self.autofocus_thread)
self.grbl.moveToThread(self.autofocus_thread)  # PROBLEM!

# In on_autofocus_finished()
self.grbl.moveToThread(self.thread())  # CAUSES ERROR!
```

**Problems:**
1. Moving GRBL to AutoFocus thread breaks polling
2. Trying to move GRB<PERSON> back causes thread error
3. No polling restart after AutoFocus completes

## Fixes Implemented

### 1. Remove GRBL Thread Movement
```python
# Before (PROBLEMATIC)
self.autofocus_worker.moveToThread(self.autofocus_thread)
self.grbl.moveToThread(self.autofocus_thread)  # Causes issues

# After (FIXED)
self.autofocus_worker.moveToThread(self.autofocus_thread)
# self.grbl.moveToThread(self.autofocus_thread)  # REMOVED
```

**Rationale:**
- GRBL should stay in main thread for UI polling
- AutoFocus worker can access GRBL across threads safely
- Avoids complex thread management issues

### 2. Remove Problematic moveToThread in Cleanup
```python
# Before (CAUSES ERROR)
def on_autofocus_finished(self, best_pos, best_score):
    self.grbl.moveToThread(self.thread())  # ERROR!

# After (FIXED)
def on_autofocus_finished(self, best_pos, best_score):
    # JANGAN moveToThread - ini menyebabkan error!
    # self.grbl.moveToThread(self.thread())  # REMOVED
```

### 3. Add GRBL Polling Restart
```python
def on_autofocus_finished(self, best_pos, best_score):
    # Restart GRBL polling untuk update posisi UI
    try:
        if hasattr(self.grbl, 'polling_timer') and not self.grbl.polling_timer.isActive():
            print("[UI] Restarting GRBL polling after AutoFocus")
            self.grbl.start_polling()
        else:
            print("[UI] GRBL polling already active or not available")
    except Exception as e:
        print(f"[UI] Error restarting GRBL polling after AutoFocus: {e}")
    
    # Update current position display
    try:
        current_x, current_y, current_z = self.grbl.get_current_position()
        self.update_position_display(current_x, current_y, current_z)
        print(f"[UI] Position updated after AutoFocus: X={current_x:.3f}, Y={current_y:.3f}, Z={current_z:.3f}")
    except Exception as e:
        print(f"[UI] Error updating position after AutoFocus: {e}")
```

## Expected Behavior After Fixes

### 1. No More Thread Errors
```
[AF] Emitting focus_finished: Z=10.0943, Score=24.42
[UI] Restarting GRBL polling after AutoFocus
[UI] Position updated after AutoFocus: X=36.00, Y=6.17, Z=10.0943
AF Selesai. Fokus terbaik di Z=10.094
```

### 2. Position Updates Work After AutoFocus
```
Before AutoFocus: Position updates ✓
During AutoFocus: Position updates paused (normal)
After AutoFocus: Position updates ✓ (FIXED!)
```

### 3. Manual Movement Position Tracking
```
After AutoFocus completes:
- Move GRBL manually → Position updates in UI ✓
- GRBL status shows current position ✓
- No stuck position display ✓
```

## Technical Details

### Thread Architecture (Fixed)
```
Main Thread:
├── UI Components
├── GRBL Object (stays here!)
├── GRBL Polling Timer
└── Position Display Updates

AutoFocus Thread:
├── AutoFocusWorker
├── Camera Operations
└── Focus Calculations
```

### Cross-Thread Communication
- AutoFocus worker accesses GRBL from different thread (safe)
- GRBL stays in main thread for polling consistency
- Signals used for thread-safe communication
- No object movement between threads

### Polling Management
- GRBL polling continues in main thread
- AutoFocus doesn't interfere with polling
- Explicit restart after AutoFocus ensures updates
- Position display refreshed after completion

## Benefits

1. **No Thread Errors**: Eliminates moveToThread conflicts
2. **Consistent Position Updates**: UI always shows current position
3. **Simpler Architecture**: GRBL stays in main thread
4. **Reliable Operation**: No complex thread management
5. **Better User Experience**: Position always visible

## Testing Checklist

- [ ] AutoFocus runs without thread errors
- [ ] Position updates work before AutoFocus
- [ ] Position updates work after AutoFocus
- [ ] Manual movement tracked after AutoFocus
- [ ] Refine AutoFocus also works correctly
- [ ] No "stuck" position display
- [ ] GRBL status shows current values

## Comparison: Before vs After

### Before (Problematic)
```
1. Start AutoFocus
2. Move GRBL to AutoFocus thread
3. AutoFocus runs
4. Try to move GRBL back → ERROR!
5. Position display stuck
```

### After (Fixed)
```
1. Start AutoFocus
2. Keep GRBL in main thread
3. AutoFocus runs (accesses GRBL safely)
4. Restart polling after completion
5. Position display works normally
```

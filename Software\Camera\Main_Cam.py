from Camera import toupcam
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QImage, QPixmap
import os
import ctypes
import cv2
import numpy as np
from configuration import config

"""
Kelas Controller untuk Kamera Toupcam.

Kelas ini dirancang untuk berjalan di dalam QThread terpisah (worker thread).
Tugasnya adalah menangani semua interaksi dengan SDK Toupcam dan berkomunikasi
dengan thread utama (UI Thread) secara eksklusif melalui sinyal dan slot Qt.

Prinsip Desain:
- Tidak memiliki dependensi pada elemen QtWidgets (tombol, label, dll).
- Semua data yang perlu ditampilkan di UI (gambar, status, nilai) dipancarkan melalui sinyal.
- Semua aksi yang diminta oleh UI (snap, ubah pengaturan) diterima melalui slot.
"""
class Main_Camera(QObject):
    # ======================================================================
    # SINYAL (KOMUNIKASI KELUAR KE UI THREAD)
    # ======================================================================

    # Sinyal saat kamera berhasil dibuka, mengirimkan nama kamera dan daftar resolusi
    camera_opened = pyqtSignal(str, list)

    # Sinyal saat kamera gagal atau ditutup
    camera_closed = pyqtSignal()

    # Sinyal yang membawa frame gambar baru yang siap ditampilkan
    frame_ready = pyqtSignal(QPixmap)

    # Sinyal yang membawa teks status FPS baru
    fps_updated = pyqtSignal(str)

    # Sinyal yang memberitahu UI bahwa gambar 'still' berhasil disimpan
    still_image_saved = pyqtSignal(str)

    # Sinyal untuk mengirim pesan error
    error_occurred = pyqtSignal(str)

    exposure_range_ready = pyqtSignal(int, int, int)
    gain_range_ready = pyqtSignal(int, int, int)

    current_settings_ready = pyqtSignal(dict)
    histogram_ready = pyqtSignal(object)
    numpy_frame_ready = pyqtSignal(np.ndarray)

    def __init__(self):
        super().__init__()
        self.hcam = None
        self.pData = None
        self.imgWidth = 0
        self.imgHeight = 0
        self.is_running = False
        self.timer = None
        self.cur = None
        self.still_count = 0
        self.has_printed_hist = False
        self.is_histogram_active = False
        self._last_numpy_frame = None # Tambahkan ini
        self._camera_fps = 0.0         # FPS kamera terkini (diupdate via onTimer)

    # ======================================================================
    # SLOT PUBLIK (API UNTUK DIPANGGIL DARI UI THREAD)
    # ======================================================================

    @pyqtSlot()
    def start(self):
        """Mencari, membuka, dan memulai kamera. Metode utama untuk memulai operasi."""
        if self.is_running:
            print("Peringatan: Perintah start diterima, tetapi kamera sudah berjalan.")
            return

        arr = toupcam.Toupcam.EnumV2()
        if not arr:
            self.error_occurred.emit("Tidak ada kamera yang ditemukan.")
            return

        self.cur = arr[0]
        self.hcam = toupcam.Toupcam.Open(self.cur.id)

        if not self.hcam:
            self.error_occurred.emit(f"Gagal membuka kamera: {self.cur.displayname}")
            return

        self.res = self.hcam.get_eSize()
        self.imgWidth = self.cur.model.res[self.res].width
        self.imgHeight = self.cur.model.res[self.res].height
        self.hcam.put_Option(toupcam.TOUPCAM_OPTION_BYTEORDER, 0)

        # Real-time mode: drop pending frames to keep latency low during heavy processing
        try:
            self.hcam.put_RealTime(1)  # 1 = realtime (drop pending frames)
            print("[CAM] RealTime mode set to 1 (drop pending frames)")
        except Exception as e:
            print(f"[CAM] Failed to set RealTime mode: {e}")

        buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
        self.pData = bytes(buffer_size)

        try:
            self.hcam.StartPullModeWithCallback(self.eventCallBack, self)
        except toupcam.HRESULTException as e:
            self.close()
            self.error_occurred.emit(f"Gagal memulai pull mode: {e}")
            return

        self.timer = QTimer()
        self.timer.timeout.connect(self.onTimer)
        self.timer.start(int(config.get("camera.fps_timer_ms", 1000)))

        self.is_running = True

        # Kirim sinyal bahwa kamera berhasil dibuka beserta data yang dibutuhkan UI
        resolution_list = [f"{res.width}x{res.height}" for res in self.cur.model.res]
        self.camera_opened.emit(self.cur.displayname, resolution_list)
        self.request_exposure_range()
        self.request_gain_range()

    @pyqtSlot()
    def stop(self):
        """Menghentikan kamera dan membersihkan semua sumber daya."""
        if not self.is_running:
            return

        if self.timer:
            self.timer.stop()
            self.timer = None

        if self.hcam:
            self.hcam.Close()

        self.hcam = None
        self.pData = None
        self.is_running = False
        print("Worker: Kamera dihentikan.")
        self.camera_closed.emit()

    @pyqtSlot()
    def request_exposure_range(self):
        if self.hcam:
            min_t, max_t, def_t = self.hcam.get_ExpTimeRange()
            self.exposure_range_ready.emit(min_t, max_t, def_t)

    @pyqtSlot()
    def request_gain_range(self):
        if self.hcam:
            min_g, max_g, def_g = self.hcam.get_ExpoAGainRange()
            self.gain_range_ready.emit(min_g, max_g, def_g)

    @pyqtSlot()
    def request_current_values(self):
        """Meminta worker untuk mengirim kembali semua nilai saat ini."""
        if self.hcam:
            self.handleExpoEvent()
            self.handleTempTintEvent()
            # Anda bisa menambahkan sinyal lain untuk status FFC, dll.

    @pyqtSlot()
    def do_awb_once(self):
        """Melakukan Auto White Balance sekali, lalu memicu update status lengkap."""
        if self.hcam and self.is_running:
            try:
                self.hcam.AwbOnce()
                QTimer.singleShot(200, self.request_current_values)
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal melakukan Auto White Balance: {e}")

    @pyqtSlot(int)
    def change_resolution(self, index):
        if not self.hcam or not self.is_running: return

        self.hcam.Stop() # Berhenti sementara
        self.res = index
        self.imgWidth = self.cur.model.res[index].width
        self.imgHeight = self.cur.model.res[index].height
        self.hcam.put_eSize(self.res)

        # Alokasi ulang buffer dengan ukuran baru
        buffer_size = toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight
        self.pData = bytes(buffer_size)

        # Mulai lagi
        self.hcam.StartPullModeWithCallback(self.eventCallBack, self)
        print(f"Worker: Resolusi diubah ke {self.imgWidth}x{self.imgHeight}")

    @pyqtSlot(int)
    def snap(self, resolution_index):
        """
        Mengambil gambar 'still' dengan resolusi yang dipilih.
        PENTING: Logika untuk menampilkan menu pilihan resolusi HARUS ada di UI.
        UI kemudian memanggil slot ini dengan indeks resolusi yang dipilih.
        """
        if self.hcam and self.is_running:
            try:
                self.hcam.Snap(resolution_index)
                print(f"Worker: Perintah snap dikirim dengan indeks resolusi {resolution_index}")
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal melakukan snap: {e}")

    @pyqtSlot(bool)
    def set_AutoExpoEnable(self, enabled):
        """Ini adalah slot yang akan menerima panggilan dari UI thread."""
        if self.hcam and self.is_running:
            print(f"Worker: Menerima perintah set_AutoExpoEnable -> {enabled}")
            self.hcam.put_AutoExpoEnable(1 if enabled else 0)

    @pyqtSlot(int)
    def set_expTime(self, time_us):
        if self.hcam and self.is_running:
            self.hcam.put_ExpoTime(time_us)

    @pyqtSlot(int)
    def set_expGain(self, gain):
        if self.hcam and self.is_running:
            self.hcam.put_ExpoAGain(gain)

    @pyqtSlot(int, int)
    def set_TempTint(self, temp, tint):
        if self.hcam and self.is_running:
            self.hcam.put_TempTint(temp, tint)

    # Tambahkan metode untuk FFC dan lainnya sebagai slot juga
    @pyqtSlot(str)
    def load_ffc(self, path):
        if self.hcam and self.is_running and os.path.isfile(path):
            try:
                self.hcam.FfcImport(path)
                self.hcam.put_Option(toupcam.TOUPCAM_OPTION_FFC, 1)
                print(f"Worker: FFC file dimuat dari {path}")
            except Exception as e:
                self.error_occurred.emit(f"Gagal memuat FFC file: {e}")

    @pyqtSlot(int)
    def capture_ffc(self, average_frames):
        if self.hcam:
            self.hcam.put_Option(toupcam.TOUPCAM_OPTION_FFC, 0xff000000 | average_frames)
            self.hcam.FfcOnce()

    @pyqtSlot(bool)
    def set_ffc_enabled(self, enabled):
        """Slot untuk mengaktifkan atau menonaktifkan Flat-Field Correction."""
        if self.hcam and self.is_running:
            try:
                # Nilai 1 untuk enable, 0 untuk disable
                val = 1 if enabled else 0
                self.hcam.put_Option(toupcam.TOUPCAM_OPTION_FFC, val)
                print(f"Worker: FFC diatur ke {'enabled' if enabled else 'disabled'}")
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal mengatur FFC option: {e}")

    # ======================================================================
    # LOGIKA INTERNAL DAN EVENT HANDLING (DIJALANKAN DI WORKER THREAD)
    # ======================================================================

    def onTimer(self):
        if self.hcam and self.is_running:
            nFrame, nTime, nTotalFrame = self.hcam.get_FrameRate()
            fps_val = (nFrame * 1000.0 / nTime) if nTime > 0 else 0.0
            self._camera_fps = fps_val
            fps_text = f"Total Frames: {nTotalFrame}, FPS: {fps_val:.1f}"
            self.fps_updated.emit(fps_text)

    @staticmethod
    def eventCallBack(nEvent, self_obj):
        if self_obj and self_obj.is_running:
             self_obj.onevtCallback(nEvent)

    def onevtCallback(self, nEvent):
        '''
        Fungsi ini sekarang hanya memicu pengiriman laporan status lengkap
        atau menangani event gambar.
        '''
        if not self.is_running: return

        if nEvent == toupcam.TOUPCAM_EVENT_IMAGE:
            self.handleImageEvent()

        # Jika ada perubahan pada eksposur atau white balance oleh kamera (misal: auto),
        # kita picu pengiriman laporan status lengkap agar UI bisa update.
        elif nEvent == toupcam.TOUPCAM_EVENT_EXPOSURE or nEvent == toupcam.TOUPCAM_EVENT_TEMPTINT:
            self.request_current_values()

        elif nEvent == toupcam.TOUPCAM_EVENT_STILLIMAGE:
            self.handleStillImageEvent()

        elif nEvent == toupcam.TOUPCAM_EVENT_ERROR or nEvent == toupcam.TOUPCAM_EVENT_DISCONNECTED:
            self.error_occurred.emit(f"Kamera error atau terputus (event: {nEvent}).")
            self.stop()

    def handleImageEvent(self):
        try:
            self.hcam.PullImageV4(self.pData, 0, 24, 0, None)
            image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(image)
            if not pixmap.isNull():
                self.frame_ready.emit(pixmap)
        except toupcam.HRESULTException as e:
            print(f"Exception di handleImageEvent: {e}")

    def handleImageEvent(self):
        """
        Sekarang, selain mengirim frame gambar, metode ini juga akan
        menghitung dan mengirim data histogram jika diminta.
        """
        try:
            self.hcam.PullImageV4(self.pData, 0, 24, 0, None)
            raw_image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))

            # Simpan frame numpy terbaru
            self._last_numpy_frame = raw_image_np.copy()

            # Pancarkan sinyal frame numpy untuk autofocus
            self.numpy_frame_ready.emit(self._last_numpy_frame)
            # --- Bagian 1: Mengirim frame gambar (tidak berubah) ---
            image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
            if not image.isNull():
                self.frame_ready.emit(QPixmap.fromImage(image))

            # --- Bagian 2: Menghitung & mengirim histogram (BARU) ---
            if self.is_histogram_active:
                # Buat array NumPy dari data buffer mentah, TANPA diubah oleh Levels
                # Ini adalah data "sebelum" pemrosesan, persis seperti yang kita mau.
                image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))

                # Pisahkan channel warna
                b, g, r = cv2.split(image_np)

                # Hitung histogram untuk setiap channel menggunakan OpenCV
                hist_r = cv2.calcHist([r], [0], None, [256], [0, 256])
                hist_g = cv2.calcHist([g], [0], None, [256], [0, 256])
                hist_b = cv2.calcHist([b], [0], None, [256], [0, 256])

                # Gabungkan hasilnya menjadi satu list 768 elemen agar sesuai
                # dengan format yang diharapkan oleh HistogramWidget kita.
                hist_list = hist_r.flatten().tolist() + hist_g.flatten().tolist() + hist_b.flatten().tolist()

                # Pancarkan sinyal dengan data histogram yang baru dihitung
                self.histogram_ready.emit(hist_list)

        except toupcam.HRESULTException as e:
            print(f"Exception di handleImageEvent: {e}")

    @pyqtSlot()
    def request_current_values(self):
        """
        Mengumpulkan SEMUA status kamera saat ini dan mengirimkannya
        dalam satu dictionary melalui sinyal 'current_settings_ready'.
        """
        if self.hcam and self.is_running:
            try:
                settings_report = {
                    'auto_exposure': bool(self.hcam.get_AutoExpoEnable()),
                    'exposure_time': self.hcam.get_ExpoTime(),
                    'exposure_gain': self.hcam.get_ExpoAGain(),
                    'temp': 0, 'tint': 0,
                    'ffc_enabled': bool(self.hcam.get_Option(toupcam.TOUPCAM_OPTION_FFC)),
                    'exp_time_range': self.hcam.get_ExpTimeRange(),
                    'exp_gain_range': self.hcam.get_ExpoAGainRange()
                }
                if (self.cur.model.flag & toupcam.TOUPCAM_FLAG_MONO) == 0:
                    temp, tint = self.hcam.get_TempTint()
                    settings_report['temp'], settings_report['tint'] = temp, tint

                self.current_settings_ready.emit(settings_report)
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal mendapatkan status kamera: {e}")

    @pyqtSlot(int)
    def capture_ffc(self, average_frames):
        """Slot untuk memulai proses FFC capture dari UI."""
        if self.hcam and self.is_running:
            try:
                # Set jumlah frame rata-rata terlebih dahulu
                self.hcam.put_Option(toupcam.TOUPCAM_OPTION_FFC, 0xff000000 | average_frames)
                # Kemudian, panggil FfcOnce untuk memulai capture
                self.hcam.FfcOnce()
                print(f"Worker: Memulai FFC capture dengan {average_frames} frame rata-rata...")
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal memulai FFC capture: {e}")

    @pyqtSlot(str)
    def save_ffc(self, file_path):
        """Slot untuk menyimpan/export data FFC ke sebuah file."""
        if self.hcam and self.is_running:
            try:
                self.hcam.FfcExport(file_path)
                print(f"Worker: FFC berhasil di-export ke: {file_path}")
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal export FFC: {e}")

    @pyqtSlot()
    def start_histogram(self):
        """Hanya mengaktifkan flag untuk mulai menghitung histogram di handleImageEvent."""
        print("Worker: Perhitungan histogram manual diaktifkan.")
        self.is_histogram_active = True

    @pyqtSlot()
    def stop_histogram(self):
        """Menonaktifkan flag untuk berhenti menghitung histogram."""
        print("Worker: Perhitungan histogram manual dihentikan.")
        self.is_histogram_active = False

    @pyqtSlot()
    def trigger_histogram_once(self):
        """
        Mengambil satu frame gambar saat ini, menghitung histogramnya secara manual,
        dan mengirimkannya sekali saja ke UI.
        """
        if not (self.hcam and self.is_running and self.pData):
            self.error_occurred.emit("Kamera tidak siap untuk mengambil single histogram.")
            return

        print("Worker: Menerima permintaan single histogram...")
        try:
            # Langkah 1: Tarik data frame terbaru secara manual saat ini juga
            self.hcam.PullImageV4(self.pData, 0, 24, 0, None)

            # Langkah 2: Lakukan kalkulasi manual (kode yang sama dari handleImageEvent)
            image_np = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))

            b, g, r = cv2.split(image_np)

            hist_r = cv2.calcHist([r], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([g], [0], None, [256], [0, 256])
            hist_b = cv2.calcHist([b], [0], None, [256], [0, 256])

            hist_list = hist_r.flatten().tolist() + hist_g.flatten().tolist() + hist_b.flatten().tolist()

            # Langkah 3: Langsung kirim hasilnya melalui sinyal
            self.histogram_ready.emit(hist_list)
            print("Worker: Single histogram snapshot berhasil dihitung dan dikirim.")

        except toupcam.HRESULTException as e:
            self.error_occurred.emit(f"Gagal mengambil frame untuk single histogram: {e}")
        except Exception as e:
            # Menangkap error lain, misal dari numpy/cv2 jika format gambar salah
            self.error_occurred.emit(f"Error saat kalkulasi single histogram: {e}")

    @pyqtSlot(int, int)
    def set_histogram_roi(self, left, right):
        """Mengatur rentang Region of Interest (ROI) untuk kalkulasi histogram."""
        if self.hcam and self.is_running:
            try:
                # PENTING: Nilai konstanta ini mungkin berbeda di library Anda.
                # Cari di toupcam.py untuk TOUPCAM_OPTION_HISTOGRAM_ROI_...
                TOUPCAM_OPTION_HISTOGRAM_ROI_LEFT = 0x06  # GANTI DENGAN NILAI YANG BENAR
                TOUPCAM_OPTION_HISTOGRAM_ROI_RIGHT = 0x07 # GANTI DENGAN NILAI YANG BENAR

                self.hcam.put_Option(TOUPCAM_OPTION_HISTOGRAM_ROI_LEFT, left)
                self.hcam.put_Option(TOUPCAM_OPTION_HISTOGRAM_ROI_RIGHT, right)
                print(f"Worker: ROI Histogram diatur ke [{left}, {right}]")
            except toupcam.HRESULTException as e:
                self.error_occurred.emit(f"Gagal mengatur ROI histogram: {e}")

    @pyqtSlot(int, int)
    def set_levels(self, black_level, white_level):
        """
        Menerima nilai master Black & White dari UI, menerjemahkannya ke format array,
        dan memanggil fungsi 'put_LevelRange' yang benar dari library.
        """
        if self.hcam and self.is_running:
            try:
                # LANGKAH 1: Terjemahkan dua angka menjadi dua array berisi 4 elemen
                # Kita asumsikan pengaturan level ini berlaku sama untuk semua channel (R,G,B,Luminance)
                low_levels_array = [black_level] * 4
                high_levels_array = [white_level] * 4

                # LANGKAH 2: Panggil fungsi library yang benar dengan format argumen yang benar
                self.hcam.put_LevelRange(low_levels_array, high_levels_array)

                print(f"Worker: LevelRange diatur ke Black={black_level}, White={white_level}")

            except toupcam.HRESULTException as e:
                # Kita beri pesan error yang lebih spesifik sekarang
                self.error_occurred.emit(f"Gagal mengatur LevelRange: {e}")

    @pyqtSlot(result=np.ndarray)
    def get_latest_numpy_frame(self):
        """Metode aman untuk mendapatkan frame terakhir dari thread lain."""
        return self._last_numpy_frame

    @pyqtSlot(result=int)
    def get_camera_total_frames(self) -> int:
        """Total frame yang dilaporkan driver kamera sejak start (delta bisa diambil untuk sesi AF)."""
        try:
            if self.hcam and self.is_running:
                _, _, nTotalFrame = self.hcam.get_FrameRate()
                return int(nTotalFrame)
        except Exception:
            pass
        return 0

    @pyqtSlot(result=float)
    def get_camera_fps(self) -> float:
        """Dapatkan FPS kamera terkini (dari Toupcam.get_FrameRate)."""
        return float(self._camera_fps)

# Slide Scanner Application - Software Code Explanation #

## Overview ##

The Slide Scanner Application is a comprehensive microscopy control system built with PyQt5 that integrates camera control, motorized stage movement, auto-focus capabilities, and image stitching. The application follows a multi-threaded architecture to maintain UI responsiveness while handling hardware communcation.

## Architecture ##

### Main Application Structure ###
- **main.py**   : Entry point that initializes the application with a splash screen and loads the main UI
- **UI.py**     : Main orchestrator that manages the user interface and coordinates between diffetenr subsystem through Qt signals/slots
- **Constans.py**   : Configuration file containing constants like key mappings, zoom settings, and icon paths

### Multi-threaded Design ###

the application follows a clean separation of concerns with:
- **UI Thread** : Handles user interactions and interfaces updates
- **Worker Threads** : Manage hardware communication (camera, motors) to prevent UI blocking
- **Communication** : User Qt's signals/slots mechanism for thread-safe communication

### Core Components ###

**Camera System**

- **Main_Cam.py** : Toupcam SDK wrapper running in a separate threan for main microscopy camera
- **Prev_Cam.py** : USB preview camera handler for real-time monitoring
- **Settings.py** : Camera configuration interface with exposure, white balanced, and FFC controls
- **RoiLabel.py** : Costum QLabel with ROI selection and pixel corrdinate conversion capabilities
- **Move_To.py**  : Coordinate conversion system from pixel coordinates to GRBL motor positions

**Motion Control**

- **Grbl/Grbl.py** : Serial communication with GRBL-controlled motors for XYZ positoning
- **Grbl/value.py** : Value manager for feedrate settings

**Auto Focus System**

- **AutoFocus_worker.py** : Two-stage coarse-to-fine focus algorithm with real-time focus scoring
- **focus_plot_window.py** : Visualization of focus curves and performance metrics

**Image Stitching**

- **Stitching.py** : Main controller coordinating the stitching process
- **GridCalculator.py** : Calculates movement grid based on ROI and overlap parameters
- **StitchingWorker.py** : Background worker for the stitching process
- **ImageStitcher.py** : mage stitching algorithms using OpenCV with phase correlation
- **StitchingUI.py** : UI components for stitching control

**Additional Features**

- **Scale_Adjust.py**: Camera setting management for different zoom levels
- **Calibration.py**: Flat-field correction tools
- **histogram_widget.py**: Real-time histogram visualization
- **content_widget.py**: Custom widgets for aspect ratio preservation
- **loading.py**: Splash screen implementation


## Key Features ##
### Multi-thread Architecture ###

- Keeps UI responsive during hardware operations
- Prevents blocking during long-running tasks like image capture or stitching

### Live Camera Streaming ###

- Multiple resolution support with optimized camera settings
- Real-time histogram visualization
- Flat-field correction for image enhancement

### Motorized Stage Control ###

- Precise XYZ positioning via GRBL
- Keyboard shortcuts for manual control
- Coordinate conversion from pixel to physical positions

### Automated Focusing ###

- Two-stage coarse-to-fine focus algorithm
- Real-time focus scoring using Sobel edge detection
- Visual feedback through focus curve plotting

### Image Stitching ###

- ROI-based stitching with configurable overlap
- Grid calculation for optimal movement paths
- Multiple stitching algorithms (OpenCV Stitcher, manual, simple grid)
- Multi-band blending for seamless results

### Zoom Presets ###

- Configurable settings for 4X, 10X, 20X, 40X magnifications
- Automatic camera parameter adjustment

## Workflow ##

1. Application Startup: Main thread initializes UI with splash screen
2. Hardware Initialization: Camera and GRBL systems start in separate threads
3. User Interaction: UI thread handles all user inputs
4. Hardware Control: Commands sent to worker threads via Qt signals
5. Data Processing: Image processing and analysis in worker threads
6. Results Display: Results sent back to UI thread for visualization

## Technical Implementation Details ##

### Thread Safety ##

- All hardware communication occurs in worker threads
- UI updates happen only in the main thread
- Qt's signal/slot mechanism ensures thread-safe communication

### Camera Integration ###

- Uses Toupcam SDK for main microscopy camera
- OpenCV for USB preview camera
- Real-time parameter adjustment through ScaleAdjust class

### Motion Control ###

- GRBL firmware for stepper motor control
- Precise positioning with configurable feedrates
- Status polling for real-time position updates

### Image Processing ###

- OpenCV for image stitching and analysis
- Sobel edge detection for focus scoring
- Phase correlation for image alignment
- Multi-band blending for seamless stitching

This architecture provides a robust, responsive application for slide scanning with professional-grade features for microscopy applications.

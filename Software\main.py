#File: Main.py

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from UI import UI
from loading import LoadingSplash
import threading

'''
Main function to run the application
'''

def main():
    app = QApplication(sys.argv)

    # <PERSON><PERSON><PERSON><PERSON> splash screen sederhana
    splash = LoadingSplash()
    splash.show()
    app.processEvents()

    # Tunda sedikit lalu tampilkan UI utama
    def load_main_window():
        main_window = UI()
        for thread in threading.enumerate():
            print(f"Thread name: {thread.name}, alive: {thread.is_alive()}")
        main_window.showMaximized()
        splash.close()
        app.main_window = main_window  # mencegah garbage collection

    QTimer.singleShot(100, load_main_window)

    sys.exit(app.exec_())


if __name__ == '__main__':
    main()

import os
import json
from Constant import *
# LANGKAH 1: Tambahkan import yang diperlukan untuk komunikasi antar-thread
from PyQt5.QtCore import QMetaObject, Qt, Q_ARG

class ScaleAdjust:
    def __init__(self, camera, ffc_dir=None, config_file='config.json'):
        """
        :param camera: Instance Main_Camera yang berjalan di worker thread.
        :param ffc_dir: Direktori tempat menyimpan file FFC (.dat)
        """
        self.camera = camera
        
        # Logika __init__ lainnya tetap sama
        if ffc_dir is None:
            # Menggunakan path relatif dari file ini
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.ffc_dir = os.path.join(script_dir, 'FFC') # Asumsi subfolder FFC
        else:
            self.ffc_dir = ffc_dir
        
        # Pastikan direktori FFC ada
        os.makedirs(self.ffc_dir, exist_ok=True)
            
        self.config_file = config_file
        if os.path.exists(self.config_file):
            self.load_from_file()
        else:
            self.reset_to_default()

    def apply(self, zoom_level):
        """
        Menerapkan pengaturan ke objek kamera di worker thread secara aman.
        """
        cfg = self.get_all_settings().get(zoom_level)

        if cfg is None:
            raise ValueError(f"[ERROR] Zoom level '{zoom_level}' tidak ditemukan dalam konfigurasi.")

        # Periksa apakah objek kamera ada sebelum mencoba mengirim perintah
        if not self.camera:
            print("[ERROR] Objek kamera belum diinisialisasi di ScaleAdjust.")
            return

        # ======================================================================
        # LANGKAH 2: Ganti semua panggilan langsung dengan QMetaObject.invokeMethod
        # Ini akan mengirim "permintaan" ke worker thread untuk menjalankan slot.
        # ======================================================================

        # Set exposure
        # Perhatikan nama slot di main_camera_refactored.py adalah "set_AutoExpoEnable"
        QMetaObject.invokeMethod(self.camera, "set_AutoExpoEnable", Qt.QueuedConnection,
                                 Q_ARG(bool, cfg.get('autoExp', False)))
                                 
        QMetaObject.invokeMethod(self.camera, "set_expTime", Qt.QueuedConnection,
                                 Q_ARG(int, cfg.get('expTime', 1000)))

        QMetaObject.invokeMethod(self.camera, "set_expGain", Qt.QueuedConnection,
                                 Q_ARG(int, cfg.get('expGain', 100)))
        
        QMetaObject.invokeMethod(self.camera, "set_TempTint", Qt.QueuedConnection,
                                 Q_ARG(int, cfg.get('temp', 5500)),
                                 Q_ARG(int, cfg.get('tint', 1000)))

        # Load FFC file
        ffc_filename = cfg.get('ffc_file')
        if ffc_filename:
            ffc_path = os.path.join(self.ffc_dir, ffc_filename)
            print(f"DEBUG: Mencoba mencari FFC di path absolut: {os.path.abspath(ffc_path)}")
            if os.path.isfile(ffc_path):
                 QMetaObject.invokeMethod(self.camera, "load_ffc", Qt.QueuedConnection,
                                          Q_ARG(str, ffc_path))
            else:
                print(f"Warning: FFC file not found at {ffc_path}")
        
        # Print info tetap bisa dilakukan di sini karena tidak berinteraksi dengan kamera
        print(f"[INFO] Perintah untuk menerapkan pengaturan {zoom_level} telah dikirim ke kamera.")

    # ======================================================================
    # Sisa dari kelas ini tidak perlu diubah karena hanya mengelola
    # kamus (dictionary) 'self.settings' dan tidak berinteraksi dengan kamera.
    # ======================================================================

    def get_expTime(self, scale):
        return self.settings[scale].get("expTime")

    def get_expGain(self, scale):
        return self.settings[scale].get("expGain")

    def get_temp(self, scale):
        return self.settings[scale].get("temp")

    def get_tint(self, scale):
        return self.settings[scale].get("tint")

    def get_ffc_file(self, scale):
        return self.settings[scale].get("ffc_file")

    def get_all_settings(self):
        return self.settings
    
    def set_expTime(self, scale, value):
        self.settings[scale]["expTime"] = value

    def set_expGain(self, scale, value):
        self.settings[scale]["expGain"] = value

    def set_temp(self, scale, value):
        self.settings[scale]["temp"] = value

    def set_tint(self, scale, value):
        self.settings[scale]["tint"] = value

    def set_ffc_file(self, scale, filename):
        self.settings[scale]["ffc_file"] = filename

    def save_to_file(self):
        try:
            with open(self.config_file, "w") as f:
                json.dump(self.settings, f, indent=4)
            print(f"[INFO] Settings saved to '{self.config_file}'")
        except Exception as e:
            print(f"[ERROR] Failed to save settings: {e}")

    def load_from_file(self):
        try:
            with open(self.config_file, "r") as f:
                self.settings = json.load(f)
            print(f"[INFO] Settings loaded from '{self.config_file}'")
        except Exception as e:
            print(f"[ERROR] Failed to load settings: {e}")
            self.reset_to_default()

    def reset_to_default(self):
        self.settings = {
            scale: values.copy()
            for scale, values in ZOOM_SETTINGS.items()
        }
        print("[INFO] Settings reset to default")
    
    def set_settings(self, scale, expTime, expGain, temp, tint):
        if scale not in self.settings:
            self.settings[scale] = {}
        self.settings[scale].update({
            'expTime': expTime,
            'expGain': expGain,
            'temp': temp,
            'tint': tint
        })
        print(f"[INFO] Updated settings for {scale}: {self.settings[scale]}")
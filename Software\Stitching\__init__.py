"""
Stitching Package - Sistem Stitching untuk Slide Scanner
Menggabungkan beberapa gambar dari area ROI menjadi satu gambar besar

Modules:
- Stitching: Main controller untuk proses stitching
- GridCalculator: Menghitung grid pergerakan berdasarkan ROI
- StitchingWorker: Worker thread untuk proses stitching
- ImageStitcher: Algoritma untuk menggabungkan gambar
- StitchingUI: UI components untuk kontrol stitching
"""

from .Stitching import StitchingController
from .GridCalculator import GridCalculator
from .StitchingWorker import StitchingWorker
from .ImageStitcher import ImageStitcher
from .StitchingUI import StitchingControlWidget, AdvancedStitchingDialog

__version__ = "1.0.0"
__author__ = "Abroror"

__all__ = [
    'StitchingController',
    'GridCalculator', 
    'StitchingWorker',
    'ImageStitcher',
    'StitchingControlWidget',
    'AdvancedStitchingDialog'
]

print("Stitching package loaded successfully")

# File: custom_widgets.py

from PyQt5.QtWidgets import QLabel
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt

class AspectRatioLabel(QLabel):
    """
    Sebuah QLabel yang secara otomatis menjaga rasio aspek dari pixmap
    yang ditampilkannya. Gambar akan di-scale agar pas di dalam label
    tanpa distorsi.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self._pixmap = QPixmap()
        self.setMinimumSize(1, 1) # Izinkan label untuk mengecil

    def setPixmap(self, pixmap):
        """Override metode setPixmap."""
        if isinstance(pixmap, QPixmap):
            self._pixmap = pixmap
            self.update_scaled_pixmap()

    def resizeEvent(self, event):
        """Panggil update saat ukuran label berubah."""
        self.update_scaled_pixmap()

    def update_scaled_pixmap(self):
        """Fungsi inti untuk scaling."""
        if self._pixmap.isNull():
            return

        # Scale pixmap dengan menjaga rasio aspek
        scaled_pixmap = self._pixmap.scaled(
            self.size(),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        # Panggil metode setPixmap dari superclass (QLabel asli) untuk menampilkan
        super().setPixmap(scaled_pixmap)

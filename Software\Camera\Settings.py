from PyQt5.QtWidgets import (QWidget, QVBoxLayout,QHBoxLayout, QLabel, QPushButton, QLineEdit, QFormLayout, QDialog, QDialogButtonBox, QMessageBox,
                             QGroupBox, QComboBox, QSplitter, QSlider, QLabel, QCheckBox, QSpinBox, QGridLayout)
from PyQt5.QtCore import QSignalBlocker, Qt, pyqtSlot, QMetaObject, Q_ARG
from Camera import toupcam
import time, cv2
from Scale_Adjust import ScaleAdjust
from Grbl.value import ValueManager
from Camera.Calibration import CalibrationDialog
from Constant import zoom_size
from Camera.histogram_widget import HistogramWidget

class SettingWindow(QDialog):
    def __init__(self, camera,grbl_instance, parent = None, preview_camera = None):
        super().__init__(parent)
        self.camera = camera
        self.preview_camera = preview_camera
        self.grbl = grbl_instance
        self.histogram_widget = HistogramWidget()
        self.ffc_done = False
        self.parent = parent
        self.scale_adjust = ScaleAdjust(self.camera)
        self.setWindowTitle("Settings")
        self.setMinimumSize(800, 600)
        self.ffc_dialog_instance = None

        self.settings = [
            ("Brightness", cv2.CAP_PROP_BRIGHTNESS, -64, 64),
            ("Contrast", cv2.CAP_PROP_CONTRAST, 0, 64),
            ("Saturation", cv2.CAP_PROP_SATURATION, 0, 128),
            ("Exposure", cv2.CAP_PROP_EXPOSURE, -10, 0),
            ("Gain", cv2.CAP_PROP_GAIN, 0, 100),
            ("Gamma", cv2.CAP_PROP_GAMMA, 72, 500)
        ]
        
        self.sliders = {}
        self.setup_ui()
        self.connect_signals_to_camera()
           
    def setup_ui(self):
        dialog_layout = QVBoxLayout(self)
        columns_layout = QHBoxLayout()

        # Panggil helper untuk membuat setiap kolom dan menambahkannya ke layout
        columns_layout.addWidget(self._create_main_cam_column(), 1)
        columns_layout.addWidget(self._create_preview_cam_column(), 1)
        columns_layout.addWidget(self._create_histogram_column(), 1)
        
        self.buttonBox = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        
        dialog_layout.addLayout(columns_layout)
        dialog_layout.addWidget(self.buttonBox)

        # Hubungkan sinyal tombol utama
        self.buttonBox.accepted.connect(self.accept)
        self.buttonBox.rejected.connect(self.reject)

    # ======================================================================
    # HELPER: MEMBUAT KOLOM 1 (KAMERA UTAMA)
    # ======================================================================
    def _create_main_cam_column(self):
        layout = QVBoxLayout()
        widget = QWidget()
        widget.setLayout(layout)

        # --- GroupBox Setting Profile ---
        self.gboxSet = QGroupBox("Setting Profile")
        vlytSet = QVBoxLayout()
        self.cmb_set = QComboBox()
        self.cmb_set.addItems(zoom_size)
        vlytSet.addWidget(self.cmb_set)
        self.gboxSet.setLayout(vlytSet)

        # --- GroupBox Resolution ---
        self.gboxres = QGroupBox("Resolution")
        vlytres = QVBoxLayout()
        self.cmb_res = QComboBox()
        vlytres.addWidget(self.cmb_res)
        self.gboxres.setLayout(vlytres)

        # --- GroupBox Exposure ---
        self.gboxexp = QGroupBox("Exposure")
        vlytexp = QVBoxLayout()
        self.cbox_exp = QCheckBox("Auto Exposure")
        self.lbl_expTime = QLabel("0")
        self.lbl_expGain = QLabel("0")
        self.slider_expTime = QSlider(Qt.Horizontal)
        self.slider_expGain = QSlider(Qt.Horizontal)
        vlytexp.addWidget(self.cbox_exp)
        vlytexp.addLayout(self.makeLayout(QLabel("Time(us):"), self.slider_expTime, self.lbl_expTime, 
                                          QLabel("Gain(%):"), self.slider_expGain, self.lbl_expGain))
        self.gboxexp.setLayout(vlytexp)

        # --- GroupBox White Balance ---
        self.gboxwb = QGroupBox("White Balance")
        vlytwb = QVBoxLayout()
        self.btn_wb = QPushButton("Auto White Balance")
        self.lbl_temp = QLabel("0")
        self.lbl_tint = QLabel("0")
        self.slider_temp = QSlider(Qt.Horizontal)
        self.slider_tint = QSlider(Qt.Horizontal)
        self.slider_temp.setRange(toupcam.TOUPCAM_TEMP_MIN, toupcam.TOUPCAM_TEMP_MAX)
        self.slider_tint.setRange(toupcam.TOUPCAM_TINT_MIN, toupcam.TOUPCAM_TINT_MAX)
        vlytwb.addLayout(self.makeLayout(QLabel("Temperature"), self.slider_temp, self.lbl_temp, 
                                         QLabel("Tint"), self.slider_tint, self.lbl_tint))
        vlytwb.addWidget(self.btn_wb)
        self.gboxwb.setLayout(vlytwb)
        
        # --- GroupBox FFC ---
        self.gbox_ffc = QGroupBox("Flat-Field Correction (FFC)")
        hbox_ffc = QVBoxLayout()
        self.chk_ffc = QCheckBox("Enable FFC Main Camera")
        self.prev_ffc = QCheckBox("Enable FFC Preview Camera")
        hbox_ffc.addWidget(self.chk_ffc)
        hbox_ffc.addWidget(self.prev_ffc)
        self.gbox_ffc.setLayout(hbox_ffc)
        
        # Atur semua kontrol ke nonaktif pada awalnya
        self.gboxexp.setEnabled(False)
        self.gboxwb.setEnabled(False)
        self.gbox_ffc.setEnabled(False)

        # Hubungkan sinyal kontrol ke slotnya masing-masing
        self.cmb_set.currentTextChanged.connect(self.update_ui_from_scale)
        self.cmb_res.currentIndexChanged.connect(self.onResolutionChanged)
        self.cbox_exp.stateChanged.connect(self.onAutoExpo)
        self.slider_expTime.valueChanged.connect(self.onExpTime)
        self.slider_expGain.valueChanged.connect(self.onExpGain)
        self.btn_wb.clicked.connect(self.onAutoWB)
        self.slider_temp.valueChanged.connect(self.onWBTemp)
        self.slider_tint.valueChanged.connect(self.onWBTint)
        self.chk_ffc.stateChanged.connect(self.onFfcEnable)
        self.prev_ffc.stateChanged.connect(self.onPrevFfc)
        
        layout.addWidget(self.gboxSet)
        layout.addWidget(self.gboxres)
        layout.addWidget(self.gboxexp)
        layout.addWidget(self.gboxwb)
        layout.addWidget(self.gbox_ffc)
        layout.addStretch()
        
        return widget

    # ======================================================================
    # HELPER: MEMBUAT KOLOM 2 (KAMERA PREVIEW & GERAKAN)
    # ======================================================================
    def _create_preview_cam_column(self):
        layout = QVBoxLayout()
        widget = QWidget()
        widget.setLayout(layout)

        # --- GroupBox Camera Settings (Preview) ---
        group = QGroupBox("Preview Camera Settings")
        grid = QGridLayout()
        # Isi dengan loop dari kode asli Anda
        self.settings = [
            ("Brightness", cv2.CAP_PROP_BRIGHTNESS, -64, 64), ("Contrast", cv2.CAP_PROP_CONTRAST, 0, 64),
            ("Saturation", cv2.CAP_PROP_SATURATION, 0, 128), ("Exposure", cv2.CAP_PROP_EXPOSURE, -10, 0),
            ("Gain", cv2.CAP_PROP_GAIN, 0, 100), ("Gamma", cv2.CAP_PROP_GAMMA, 72, 500)
        ]
        for i, (label_text, prop_id, min_val, max_val) in enumerate(self.settings):
            label, value_label, slider, spin = QLabel(label_text), QLabel(), QSlider(Qt.Horizontal), QSpinBox()
            slider.setRange(min_val, max_val); spin.setRange(min_val, max_val)
            if self.preview_camera and self.preview_camera.cap.isOpened():
                val = self.preview_camera.cap.get(prop_id)
                if val != -1: slider.setValue(int(val)); spin.setValue(int(val)); value_label.setText(str(int(val)))
            slider.valueChanged.connect(spin.setValue)
            spin.valueChanged.connect(slider.setValue)
            spin.valueChanged.connect(lambda val, pid=prop_id: self.set_camera_prop(pid, val))
            spin.valueChanged.connect(lambda val, label=value_label: label.setText(str(val)))
            self.sliders[prop_id] = (slider, spin)
            row = i * 2
            grid.addWidget(label, row, 0); grid.addWidget(value_label, row, 1, alignment=Qt.AlignRight)
            grid.addWidget(slider, row + 1, 0, 1, 2)
        group.setLayout(grid)

        # --- GroupBox Movement ---
        movement_group = QGroupBox("Movement")
        movement_layout = QVBoxLayout()
        value = ValueManager()
        self.feedrate_label, self.feedrate_value = QLabel("Feedrate"), QLabel(str(value.feedrate()))
        self.feedrate_slider = QSlider(Qt.Horizontal); self.feedrate_slider.setRange(1, 500); self.feedrate_slider.setValue(value.feedrate())
        self.feedrate_slider.valueChanged.connect(self.feedrate_value.setNum)
        self.feedrate_slider.valueChanged.connect(lambda val: value.set_feedrate(val))
        feedrate_top = QHBoxLayout(); feedrate_top.addWidget(self.feedrate_label); feedrate_top.addWidget(self.feedrate_value, alignment=Qt.AlignRight)
        movement_layout.addLayout(feedrate_top); movement_layout.addWidget(self.feedrate_slider)
        button_layout = QHBoxLayout()
        btn_move_left = QPushButton("Home"); btn_move_left.clicked.connect(self.Home)
        btn_move_right = QPushButton("Reset")
        button_layout.addWidget(btn_move_left); button_layout.addWidget(btn_move_right)
        movement_layout.addLayout(button_layout)
        movement_group.setLayout(movement_layout)

        # --- GroupBox Calibration ---
        calib_group = QGroupBox("Calibration")
        calib_layout = QVBoxLayout()
        self.calib_btn = QPushButton("Capture FFC & Calibrate")
        self.calib_btn.clicked.connect(self.calibration)
        calib_layout.addWidget(self.calib_btn)
        calib_group.setLayout(calib_layout)

        layout.addWidget(group)
        layout.addWidget(movement_group)
        layout.addWidget(calib_group)
        layout.addStretch()
        
        return widget

    # ======================================================================
    # HELPER: MEMBUAT KOLOM 3 (HISTOGRAM)
    # ======================================================================
    def _create_histogram_column(self):
        layout = QVBoxLayout()
        widget = QWidget()
        widget.setLayout(layout)

        gbox_histogram = QGroupBox("Histogram")
        hist_layout = QVBoxLayout()
        self.histogram_widget = HistogramWidget()
        hist_layout.addWidget(self.histogram_widget)
        gbox_histogram.setLayout(hist_layout)

        gbox_hist_controls = QGroupBox("Kontrol Histogram")
        grid_hist = QGridLayout()
        self.cmb_hist_mode = QComboBox(); self.cmb_hist_mode.addItems(["Disable", "Continue", "Once"])
        self.cmb_hist_channel = QComboBox(); self.cmb_hist_channel.addItems(["RGB", "Luminance", "Red", "Green", "Blue"])
        self.cmb_hist_scale = QComboBox(); self.cmb_hist_scale.addItems(["Linear", "Logarithmic"])
        self.spin_hist_left = QSpinBox(); self.spin_hist_left.setRange(0, 254)
        self.spin_hist_right = QSpinBox(); self.spin_hist_right.setRange(1, 255); self.spin_hist_right.setValue(255)
        
        grid_hist.addWidget(QLabel("Mode:"), 0, 0); grid_hist.addWidget(self.cmb_hist_mode, 0, 1)
        grid_hist.addWidget(QLabel("Channel:"), 1, 0); grid_hist.addWidget(self.cmb_hist_channel, 1, 1)
        grid_hist.addWidget(QLabel("Skala:"), 2, 0); grid_hist.addWidget(self.cmb_hist_scale, 2, 1)
        grid_hist.addWidget(QLabel("Levels Left:"), 3, 0); grid_hist.addWidget(self.spin_hist_left, 3, 1)
        grid_hist.addWidget(QLabel("Levels Right:"), 4, 0); grid_hist.addWidget(self.spin_hist_right, 4, 1)
        gbox_hist_controls.setLayout(grid_hist)

        # Hubungkan sinyal kontrol histogram
        self.cmb_hist_mode.currentIndexChanged.connect(self.on_hist_mode_changed)
        self.cmb_hist_channel.currentTextChanged.connect(self.histogram_widget.set_channel_mode)
        self.cmb_hist_scale.currentTextChanged.connect(self.histogram_widget.set_scale_mode)
        self.spin_hist_left.valueChanged.connect(self.on_levels_changed)
        self.spin_hist_right.valueChanged.connect(self.on_levels_changed)

        layout.addWidget(gbox_histogram)
        layout.addWidget(gbox_hist_controls)
        layout.addStretch()

        return widget
        
    def makeLayout(self, lbl1, sli1, val1, 
                     lbl2, sli2, val2):
        
        hlyt1 = QHBoxLayout()
        hlyt1.addWidget(lbl1)
        hlyt1.addStretch()
        hlyt1.addWidget(val1)
 
        hlyt2 = QHBoxLayout()
        hlyt2.addWidget(lbl2)
        hlyt2.addStretch()
        hlyt2.addWidget(val2)

        vlyt = QVBoxLayout()
        vlyt.addLayout(hlyt1)
        vlyt.addWidget(sli1)
        vlyt.addLayout(hlyt2)
        vlyt.addWidget(sli2)

        return vlyt
    
    def connect_signals_to_camera(self):
        """Hubungkan sinyal dari worker kamera ke slot di jendela setting ini."""
        if self.camera:
            self.camera.current_settings_ready.connect(self.apply_camera_settings_to_ui)
            self.camera.histogram_ready.connect(self.histogram_widget.update_histogram)
            if hasattr(self.parent, 'camera_resolutions') and self.parent.camera_resolutions:
                 self.populate_resolutions(self.parent.camera_resolutions)
    
    def showEvent(self, event):
        """Setiap kali jendela ditampilkan, minta status terbaru dari kamera."""
        super().showEvent(event)
        if self.camera:
            print("Settings Window: Meminta laporan status lengkap dari worker...")
            QMetaObject.invokeMethod(self.camera, "request_current_values", Qt.QueuedConnection)

    
    def current_scale(self):
        return self.cmb_set.currentText()
    
    def update_ui_from_scale(self, scale):
        self.slider_expGain.blockSignals(True)
        self.slider_expTime.blockSignals(True)
        self.slider_temp.blockSignals(True)
        self.slider_tint.blockSignals(True)

        self.slider_expTime.setValue(self.scale_adjust.get_expTime(scale))
        self.lbl_expTime.setText(str(self.scale_adjust.get_expTime(scale)))
        self.slider_expGain.setValue(self.scale_adjust.get_expGain(scale))
        self.lbl_expGain.setText(str(self.scale_adjust.get_expGain(scale)))
        self.temp = (self.scale_adjust.get_temp(scale))
        self.tint = (self.scale_adjust.get_tint(scale))
        self.slider_temp.setValue(self.scale_adjust.get_temp(scale))
        self.lbl_temp.setText(str(self.scale_adjust.get_temp(scale)))
        self.slider_tint.setValue(self.scale_adjust.get_tint(scale))
        self.lbl_tint.setText(str(self.scale_adjust.get_tint(scale)))

        self.slider_expGain.blockSignals(False)
        self.slider_expTime.blockSignals(False)
        self.slider_temp.blockSignals(False)
        self.slider_tint.blockSignals(False)

    def set_exp_time(self, value):
        self.scale_adjust.set_expTime(self.current_scale(), value)

    def set_exp_gain(self, value):
        self.scale_adjust.set_expGain(self.current_scale(), value)

    def set_temp(self, value):
        self.scale_adjust.set_temp(self.current_scale(), value)

    def set_tint(self, value):
        self.scale_adjust.set_tint(self.current_scale(), value)

    def save_settings(self):
        scale = self.cmb_set.currentText()
        expTime = self.slider_expTime.value()
        expGain = self.slider_expGain.value()
        temp = self.slider_temp.value()
        tint = self.slider_tint.value()

        self.scale_adjust.set_settings(scale, expTime, expGain, temp, tint)
        self.scale_adjust.save_to_file()
        self.scale_adjust.load_from_file()

        QMessageBox.information(self, "Saved", f"Settings for {scale} saved.")

    def reset(self):
        scale = self.cmb_set.currentText()
        self.scale_adjust.reset_to_default()
        self.scale_adjust.save_to_file()
        self.update_ui_from_scale(self.cmb_set.currentText())
        print("[INFO] Settings reset to default.")

    def load_resolutions(self):
        """Memuat daftar resolusi dari kamera dan mengisi ComboBox."""
        resolution_list = self.camera.get_resolution_list()  # Ambil daftar resolusi
        if resolution_list:
            with QSignalBlocker(self.cmb_res):
                self.cmb_res.clear()
                self.cmb_res.addItems(resolution_list)  # Isi ComboBox dengan resolusi
                self.cmb_res.setCurrentIndex(self.camera.res)
    
    def onResolutionChanged(self, index):
        if index >= 0:
            QMetaObject.invokeMethod(self.camera, "change_resolution", Qt.QueuedConnection, Q_ARG(int, index))

    def onAutoExpo(self, state):
        is_enabled = bool(state)
        self.slider_expTime.setEnabled(not is_enabled)
        self.slider_expGain.setEnabled(not is_enabled)
        QMetaObject.invokeMethod(self.camera, "set_AutoExpoEnable", Qt.QueuedConnection, Q_ARG(bool, is_enabled))


    def onExpTime(self, val):
        self.lbl_expTime.setText(str(val))
        QMetaObject.invokeMethod(self.camera, "set_expTime", Qt.QueuedConnection, Q_ARG(int, val))

    def onExpGain(self, val):
        self.lbl_expGain.setText(str(val))
        QMetaObject.invokeMethod(self.camera, "set_expGain", Qt.QueuedConnection, Q_ARG(int, val))

    def onAutoWB(self):
        QMetaObject.invokeMethod(self.camera, "do_awb_once", Qt.QueuedConnection)

    def onWBTemp(self, val):
        self.lbl_temp.setText(str(val))
        current_tint = self.slider_tint.value()
        QMetaObject.invokeMethod(self.camera, "set_TempTint", Qt.QueuedConnection, Q_ARG(int, val), Q_ARG(int, current_tint))

    
    def onWBTint(self, val):
        self.lbl_tint.setText(str(val))
        current_temp = self.slider_temp.value()
        QMetaObject.invokeMethod(self.camera, "set_TempTint", Qt.QueuedConnection, Q_ARG(int, current_temp), Q_ARG(int, val))

    def onFfcEnable(self, state):
        is_enabled = bool(state)
        QMetaObject.invokeMethod(self.camera, "set_ffc_enabled", Qt.QueuedConnection, Q_ARG(bool, is_enabled))
        
    
    def changeSettings(self):
        self.slider_expTime.setRange(self.camera.min_expTime, self.camera.max_expTime)
        self.slider_expGain.setRange(self.camera.min_expGain, self.camera.max_expGain)
        self.lbl_expTime.setText(str(self.camera.hcam.get_ExpoTime()))
        self.lbl_expGain.setText(str(self.camera.hcam.get_ExpoAGain()))
        
        # Set nilai awal dari kamera
        self.cbox_exp.setChecked(self.camera.hcam.get_AutoExpoEnable())
        self.slider_expTime.setValue(self.camera.hcam.get_ExpoTime())
        self.slider_expGain.setValue(self.camera.hcam.get_ExpoAGain())
        if self.camera.is_color_camera:
            temp, tint = self.camera.hcam.get_TempTint()
            self.slider_temp.setValue(temp)
            self.slider_tint.setValue(tint)
            print("a")
    
    def refresh_ui(self):
        """Ambil nilai TERBARU dari kamera/kamera"""
        if self.camera.hcam:
            # Auto Exposure
            auto_expo = self.camera.hcam.get_AutoExpoEnable()
            self.cbox_exp.setChecked(auto_expo)
            
            # Exposure Time
            expo_time = self.camera.hcam.get_ExpoTime()
            self.slider_expTime.setValue(expo_time)
            self.lbl_expTime.setText(str(expo_time))
            
            # Exposure Gain
            expo_gain = self.camera.hcam.get_ExpoAGain()
            self.slider_expGain.setValue(expo_gain)
            self.lbl_expGain.setText(str(expo_gain))
            
            # White Balance
            if self.camera.is_color_camera:
                temp, tint = self.camera.hcam.get_TempTint()
                self.slider_temp.setValue(temp)
                self.slider_tint.setValue(tint)
                self.lbl_temp.setText(str(temp))
                self.lbl_tint.setText(str(tint))

    def onFfcCapture(self):
        """Mulai flat-field capture dan nonaktifkan FFC sampai selesai."""
        if not self.camera.hcam:
            return
        # reset flag & tombol
        self.chk_ffc.setChecked(False)
        self.chk_ffc.setEnabled(False)

        # set average count
        n = self.spn_ffc_avg.value()
        self.camera.hcam.put_Option(toupcam.TOUPCAM_OPTION_FFC, 0xff000000 | n)
        # start capture
        self.camera.hcam.FfcOnce()
    
    def onPrevFfc(self, state):
        if self.preview_camera:
            self.preview_camera.ffc_enabled = (state == Qt.Checked)
            print(f"[INFO] Preview FFC set to {self.preview_camera.ffc_enabled}")
        else:
            print("[WARNING] Preview camera tidak tersedia.")
    
    def set_camera_prop(self, prop_id, value):
        self.preview_camera.cap.set(prop_id, float(value))
    
    def Home(self):
        self.grbl.Home()
    
    def calibration(self):
        """Membuka atau memfokuskan dialog FFC yang modeless (tidak memblokir)."""
        
        reply = QMessageBox.question(
            self, "Konfirmasi FFC",
            "Ini akan memulai proses Flat-Field Correction.\n\n"
            "Pastikan tidak ada preparat di mikroskop dan bidang pandang sudah bersih dan terang.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        if not self.camera:
            QMessageBox.critical(self, "Error", "Main Camera tidak tersedia untuk FFC.")
            return

        # LOGIKA BARU UNTUK DIALOG MODELESS
        # Cek apakah dialog belum pernah dibuat ATAU sudah ditutup
        if self.ffc_dialog_instance is None or not self.ffc_dialog_instance.isVisible():
            # Jika belum ada, buat instance baru dan simpan
            self.ffc_dialog_instance = CalibrationDialog(self.camera, setting_window=self) # `self` sebagai parent
            self.ffc_dialog_instance.show() # Gunakan .show() BUKAN .exec_()
        else:
            # Jika sudah ada dan terbuka, bawa saja ke depan
            self.ffc_dialog_instance.raise_()
            self.ffc_dialog_instance.activateWindow()

    def on_calibration_saved(self, new_scale_value):
        """
        Slot yang menerima nilai kalibrasi baru setelah disimpan.
        Anda bisa melakukan sesuatu dengan nilai ini di sini.
        """
        print(f"Menerima nilai kalibrasi baru dari dialog: {new_scale_value} µm/pixel")
        # Contoh: Perbarui label di suatu tempat atau simpan ke instance lain
        # self.parent.update_scale_label(new_scale_value)

    @pyqtSlot(list)
    def populate_resolutions(self, resolutions):
        with QSignalBlocker(self.cmb_res):
            self.cmb_res.clear()
            self.cmb_res.addItems(resolutions)
            self.cmb_res.setEnabled(True)
            # Anda perlu cara untuk mendapatkan indeks resolusi saat ini dari kamera
            # Untuk sekarang, kita set ke 0
            # self.cmb_res.setCurrentIndex(current_res_index) 

    
    @pyqtSlot(dict)
    def apply_camera_settings_to_ui(self, settings):
        """
        Menerima laporan status lengkap (dictionary) dan mengatur SEMUA
        elemen UI agar sesuai.
        """
        print("Settings Window: Menerima laporan status, memperbarui UI...")
        
        with QSignalBlocker(self.cbox_exp), \
             QSignalBlocker(self.slider_expTime), \
             QSignalBlocker(self.slider_expGain), \
             QSignalBlocker(self.slider_temp), \
             QSignalBlocker(self.slider_tint), \
             QSignalBlocker(self.chk_ffc):

            # Atur rentang slider terlebih dahulu
            exp_time_min, exp_time_max, _ = settings['exp_time_range']
            exp_gain_min, exp_gain_max, _ = settings['exp_gain_range']
            self.slider_expTime.setRange(exp_time_min, exp_time_max)
            self.slider_expGain.setRange(exp_gain_min, exp_gain_max)

            # Atur nilai widget dari laporan
            self.cbox_exp.setChecked(settings['auto_exposure'])
            self.slider_expTime.setValue(settings['exposure_time'])
            self.slider_expGain.setValue(settings['exposure_gain'])
            self.slider_temp.setValue(settings['temp'])
            self.slider_tint.setValue(settings['tint'])
            self.chk_ffc.setChecked(settings['ffc_enabled'])

            # Perbarui juga label teks
            self.lbl_expTime.setText(str(settings['exposure_time']))
            self.lbl_expGain.setText(str(settings['exposure_gain']))
            self.lbl_temp.setText(str(settings['temp']))
            self.lbl_tint.setText(str(settings['tint']))
            
        is_auto_expo_on = settings['auto_exposure']
        self.slider_expTime.setEnabled(not is_auto_expo_on)
        self.slider_expGain.setEnabled(not is_auto_expo_on)
        
        self.gboxexp.setEnabled(True)
        self.gboxwb.setEnabled(True)
        self.gbox_ffc.setEnabled(True)
    
    def on_hist_mode_changed(self, index):
        if not self.camera: return
        if index == 0: # Disable
            QMetaObject.invokeMethod(self.camera, "stop_histogram", Qt.QueuedConnection)
        elif index == 1: # Continue
            QMetaObject.invokeMethod(self.camera, "start_histogram", Qt.QueuedConnection)
        elif index == 2: # Once
            QMetaObject.invokeMethod(self.camera, "trigger_histogram_once", Qt.QueuedConnection)

    def on_levels_changed(self):
        """
        Dipanggil saat nilai SpinBox Left atau Right berubah.
        Melakukan dua hal:
        1. Mengirim perintah ke worker untuk mengubah gambar.
        2. Mengirim perintah ke widget histogram untuk menggeser garis visual.
        """
        if not self.camera: return
        
        left_val = self.spin_hist_left.value()
        right_val = self.spin_hist_right.value()

        if left_val >= right_val:
            # Mencegah nilai kiri melewati kanan dengan me-resetnya
            self.spin_hist_left.setValue(right_val - 1)
            return

        # 1. Perintahkan worker untuk mengubah level gambar
        QMetaObject.invokeMethod(self.camera, "set_levels", Qt.QueuedConnection,
                                 Q_ARG(int, left_val), Q_ARG(int, right_val))
                                 
        # 2. Perintahkan widget histogram untuk menggambar ulang garisnya
        if self.histogram_widget:
            self.histogram_widget.set_level_lines(left_val, right_val)

    
    @pyqtSlot(int)
    def on_show_histogram_toggled(self, state):
        """Mengirim perintah start/stop histogram ke worker thread."""
        if self.camera:
            if state == Qt.Checked:
                QMetaObject.invokeMethod(self.camera, "start_histogram", Qt.QueuedConnection)
            else:
                QMetaObject.invokeMethod(self.camera, "stop_histogram", Qt.QueuedConnection)

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog,
                             QFrame, QMessageBox, QDialogButtonBox, QComboBox, QCheckBox)
from PyQt5.QtGui import <PERSON><PERSON>ont, QPixmap
# Import komponen yang diperlukan untuk threading
from PyQt5.QtCore import Qt, pyqtSignal, QMetaObject, Q_ARG, pyqtSlot

from content_widget import AspectRatioLabel
from Constant import zoom_size
import os
import time
import subprocess

class CalibrationDialog(QDialog):
    def __init__(self, main_camera_obj, setting_window, parent=None):
        super().__init__(parent)
        self.main_camera = main_camera_obj
        self.setting_window = setting_window

        self.setWindowTitle("Flat-Field Correction (FFC) Tool")
        self.setMinimumSize(800, 600)
        self.setModal(False)

        self.setup_ui()
        self.connect_signals()

        # Hubungkan feed kamera ke slot di dialog ini
        if self.main_camera:
            # Gunakan sinyal 'frame_ready' yang benar dari worker kita
            self.main_camera.frame_ready.connect(self.update_video_feed)
        else:
            print("Peringatan: main_camera tidak valid. Tampilan kamera tidak akan muncul.")

    def setup_ui(self):
        # ... (Logika setup_ui Anda tidak perlu diubah, sudah bagus) ...
        main_layout = QVBoxLayout(self)
        instruction_label = QLabel(
            "<b>Instruksi untuk Flat-Field Correction:</b><br>"
            "1. Pilih Zoom Size yang hendak di kalibrasi.<br>"
            "2. Gerakan bidang pandang menuju tempat kosong (di ujung preparat)<br>"
            "3. Pastikan bidang pandang terang, bersih, dan seragam (tanpa debu atau goresan).<br>"
            "4. Atur fokus dan pencahayaan hingga mendapatkan gambar putih/abu-abu yang merata.<br>"
            "5. Klik tombol <b>'Capture Flat-Field'</b> untuk memulai proses.<br>"
            "6. Klik tombol <b> 'Save' </b> untuk menyimpan file FFC."
        )
        instruction_label.setWordWrap(True)
        self.camera_view_label = AspectRatioLabel()
        self.camera_view_label.setFrameShape(QFrame.Box)
        self.camera_view_label.setStyleSheet("background-color: #333;")
        self.camera_view_label.setAlignment(Qt.AlignCenter)
        button_layout = QHBoxLayout()
        self.capture_btn = QPushButton("Capture Flat-Field")
        self.capture_btn.setFixedHeight(40)
        self.close_btn = QPushButton("Close")
        self.close_btn.setFixedHeight(40)
        self.save_btn = QPushButton("Save")
        self.save_btn.setFixedHeight(40)
        self.cmb = QComboBox()
        self.cmb.setFixedHeight(40)
        self.cmb.addItems(zoom_size)
        self.cmb.currentIndexChanged.connect(self.sync_to_setting)
        self.enable = QCheckBox("Enabled")
        self.enable.stateChanged.connect(self.onFfcEnable)
        self.enable.setChecked(True)
        self.enable.setFixedHeight(40) 
        button_layout.addStretch()
        button_layout.addWidget(self.cmb)
        button_layout.addWidget(self.enable)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.capture_btn)
        button_layout.addWidget(self.close_btn)
        button_layout.addStretch()
        main_layout.addWidget(instruction_label)
        main_layout.addWidget(self.camera_view_label, 1)
        main_layout.addLayout(button_layout)


    def connect_signals(self):
        self.capture_btn.clicked.connect(self.trigger_ffc_capture)
        self.save_btn.clicked.connect(self.save_ffc_file) # Ganti nama agar lebih jelas
        self.close_btn.clicked.connect(self.reject)

    @pyqtSlot(QPixmap)
    def update_video_feed(self, pixmap):
        """Slot aman untuk menerima frame dan menampilkannya."""
        self.camera_view_label.setPixmap(pixmap)

    def trigger_ffc_capture(self):
        """MENGIRIM PERINTAH FFC capture ke worker thread."""
        if not self.main_camera:
            QMessageBox.critical(self, "Error", "Objek kamera utama tidak valid.")
            return

        # Kirim perintah ke slot 'capture_ffc' di worker dengan argumen jumlah frame
        average_frames = 5 # Bisa juga diambil dari QSpinBox di UI
        QMetaObject.invokeMethod(self.main_camera, "capture_ffc", Qt.QueuedConnection,
                                 Q_ARG(int, average_frames))
        QMessageBox.information(self, "FFC", f"Perintah capture dengan {average_frames} frame telah dikirim ke kamera.")

    def onFfcEnable(self, state):
        """MENGIRIM PERINTAH untuk enable/disable FFC ke worker thread."""
        if not self.main_camera: return
        
        is_enabled = bool(state)
        QMetaObject.invokeMethod(self.main_camera, "set_ffc_enabled", Qt.QueuedConnection,
                                 Q_ARG(bool, is_enabled))

    def save_ffc_file(self):
        """Menampilkan dialog simpan, lalu MENGIRIM PERINTAH save ke worker."""
        if not self.main_camera:
             QMessageBox.critical(self, "Error", "Objek kamera utama tidak valid.")
             return
        
        zoom_label = self.cmb.currentText()
        default_filename = f"ffc_{zoom_label}.ffc"
        default_dir = os.path.join(os.getcwd(), "FFC")
        os.makedirs(default_dir, exist_ok=True)
        suggested_path = os.path.join(default_dir, default_filename)

        file_path, _ = QFileDialog.getSaveFileName(self, "Simpan FFC", suggested_path, "FFC Files (*.ffc);;All Files (*)")

        if file_path:
            # Kirim perintah ke slot 'save_ffc' di worker dengan argumen path file
            QMetaObject.invokeMethod(self.main_camera, "save_ffc", Qt.QueuedConnection,
                                     Q_ARG(str, file_path))
            QMessageBox.information(self, "FFC", f"Perintah untuk menyimpan FFC ke:\n{file_path}\ntelah dikirim.")
        else:
            print("Penyimpanan FFC dibatalkan.")
            
    def closeEvent(self, event):
        """Memutuskan koneksi sinyal saat dialog ditutup."""
        if self.main_camera:
            try:
                self.main_camera.frame_ready.disconnect(self.update_video_feed)
            except TypeError:
                pass # Sinyal mungkin sudah terputus atau tidak pernah terhubung
        super().closeEvent(event)
    
    def sync_to_setting(self, index):
        # ... (Logika ini tidak berubah dan seharusnya aman) ...
        self.setting_window.cmb_set.setCurrentIndex(index)
        scale = self.setting_window.cmb_set.currentText()
        self.setting_window.update_ui_from_scale(scale)
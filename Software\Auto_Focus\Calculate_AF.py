"""
Calculate_AF.py - Modul untuk Perhitungan Focus Score
Memisahkan algoritma perhitungan focus score dari AutoFocus_Worker untuk modularitas yang lebih baik
"""

import cv2
import numpy as np
import time
from typing import Tuple, Optional

class FocusCalculator:
    """Class untuk menghitung focus score dari frame gambar"""

    def __init__(self):
        """Inisialisasi Focus Calculator"""
        # Method yang tersedia
        self.available_methods = [
            "sobel_gradient",     # Default method (Sobel gradient magnitude)
            "laplacian_variance", # Laplacian variance
            "brenner_gradient",   # Brenner gradient
            "tenengrad",          # Tenengrad
            "variance_of_laplacian", # Variance of Laplacian
            "roi_sobel_gradient",     # ROI-based Sobel gradient (9 points)
            "roi_laplacian_variance", # ROI-based Laplacian variance (9 points)
            "roi_brenner_gradient",   # ROI-based Brenner gradient (9 points)
            "roi_tenengrad",          # ROI-based Tenengrad (9 points)
            "roi_variance_of_laplacian" # ROI-based Variance of Laplacian (9 points)
        ]

        self.current_method = "sobel_gradient"

        print("FocusCalculator: Initialized")
        print(f"Available methods: {self.available_methods}")
        print(f"Current method: {self.current_method}")
        print("Each method has optimized parameters built-in")

    def set_method(self, method: str):
        """
        Set method untuk perhitungan focus score

        Args:
            method: Nama method yang akan digunakan
        """
        if method in self.available_methods:
            self.current_method = method
            print(f"FocusCalculator: Method changed to {method}")
        else:
            print(f"FocusCalculator: Invalid method '{method}'. Available: {self.available_methods}")

    def calculate_focus_score(self, frame: np.ndarray) -> Tuple[float, float]:
        """
        Menghitung focus score dari frame gambar

        Args:
            frame: Frame gambar dalam format BGR

        Returns:
            Tuple[float, float]: (focus_score, calculation_time)
        """
        if frame is None or frame.size == 0:
            return 0.0, 0.0

        start_time = time.perf_counter()

        try:
            # Dispatch ke method yang sesuai
            if self.current_method == "sobel_gradient":
                score = self._calculate_sobel_gradient(frame)
            elif self.current_method == "laplacian_variance":
                score = self._calculate_laplacian_variance(frame)
            elif self.current_method == "brenner_gradient":
                score = self._calculate_brenner_gradient(frame)
            elif self.current_method == "tenengrad":
                score = self._calculate_tenengrad(frame)
            elif self.current_method == "variance_of_laplacian":
                score = self._calculate_variance_of_laplacian(frame)
            # ROI-based methods
            elif self.current_method == "roi_sobel_gradient":
                score = self._calculate_roi_sobel_gradient(frame)
            elif self.current_method == "roi_laplacian_variance":
                score = self._calculate_roi_laplacian_variance(frame)
            elif self.current_method == "roi_brenner_gradient":
                score = self._calculate_roi_brenner_gradient(frame)
            elif self.current_method == "roi_tenengrad":
                score = self._calculate_roi_tenengrad(frame)
            elif self.current_method == "roi_variance_of_laplacian":
                score = self._calculate_roi_variance_of_laplacian(frame)
            else:
                # Fallback ke sobel gradient
                score = self._calculate_sobel_gradient(frame)

            calculation_time = time.perf_counter() - start_time
            return score, calculation_time

        except Exception as e:
            print(f"FocusCalculator: Error calculating focus score: {e}")
            calculation_time = time.perf_counter() - start_time
            return 0.0, calculation_time

    def _calculate_sobel_gradient(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Sobel gradient magnitude
        Parameter optimal: resize_factor=0.2, ksize=1, weights=(0.5, 0.5)
        Threshold range: 0-50, good_threshold=15
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.2
        sobel_ksize = 1
        weight_gx = 0.5
        weight_gy = 0.5

        # Convert ke grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Hitung gradient menggunakan Sobel
        gx = cv2.Sobel(small_gray, cv2.CV_16S, 1, 0, ksize=sobel_ksize)
        gy = cv2.Sobel(small_gray, cv2.CV_16S, 0, 1, ksize=sobel_ksize)

        # Convert ke absolute values
        abs_gx = cv2.convertScaleAbs(gx)
        abs_gy = cv2.convertScaleAbs(gy)

        # Combine gradients dengan weighted average
        gradient_magnitude = cv2.addWeighted(abs_gx, weight_gx, abs_gy, weight_gy, 0)

        # Return mean sebagai focus score
        return float(gradient_magnitude.mean())

    def _calculate_laplacian_variance(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan variance of Laplacian
        Parameter optimal: resize_factor=0.3, ksize=3 (untuk speed dan accuracy balance)
        Threshold range: 0-1000, good_threshold=100
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.2
        ksize = 1

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Hitung Laplacian dengan kernel size optimal
        laplacian = cv2.Laplacian(small_gray, cv2.CV_64F, ksize=ksize)

        # Return variance sebagai focus score
        return float(laplacian.var())

    def _calculate_brenner_gradient(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Brenner gradient
        Parameter optimal: resize_factor=0.4, step=2 (untuk precision dan speed balance)
        Threshold range: 0-100000, good_threshold=10000
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.4
        step = 2  # Step size untuk difference calculation

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Brenner gradient: sum of squared differences
        h, w = small_gray.shape
        brenner_score = 0.0

        # Horizontal differences dengan step optimal
        if w > step:
            diff_h = small_gray[:, step:].astype(np.float32) - small_gray[:, :-step].astype(np.float32)
            brenner_score += np.sum(diff_h ** 2)

        # Vertical differences dengan step optimal
        if h > step:
            diff_v = small_gray[step:, :].astype(np.float32) - small_gray[:-step, :].astype(np.float32)
            brenner_score += np.sum(diff_v ** 2)

        return float(brenner_score)

    def _calculate_tenengrad(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Tenengrad
        Parameter optimal: resize_factor=0.25, ksize=3, threshold_factor=0.1
        Threshold range: 0-50000, good_threshold=5000
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.25
        ksize = 3
        threshold_factor = 0.1  # Threshold sebagai persentase dari mean

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Sobel gradients dengan kernel size optimal
        gx = cv2.Sobel(small_gray, cv2.CV_64F, 1, 0, ksize=ksize)
        gy = cv2.Sobel(small_gray, cv2.CV_64F, 0, 1, ksize=ksize)

        # Gradient magnitude squared
        gradient_magnitude_sq = gx**2 + gy**2

        # Threshold optimal untuk mengurangi noise
        threshold = np.mean(gradient_magnitude_sq) * threshold_factor
        tenengrad_score = np.sum(gradient_magnitude_sq[gradient_magnitude_sq > threshold])

        return float(tenengrad_score)

    def _calculate_variance_of_laplacian(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan variance of Laplacian (alternatif implementation)
        Parameter optimal: resize_factor=0.35, ksize=1 (untuk speed maksimal)
        Threshold range: 0-800, good_threshold=80
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.35
        ksize = 1  # Kernel size kecil untuk speed

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Laplacian dengan kernel size optimal untuk speed
        laplacian = cv2.Laplacian(small_gray, cv2.CV_64F, ksize=ksize)

        return float(laplacian.var())

    def _extract_9_point_rois(self, frame: np.ndarray, roi_size: int = 100) -> list:
        """
        Ekstrak 9 ROI dari image dengan ukuran roi_size x roi_size
        ROI diambil dari 9 titik: 4 corner, 4 edge center, 1 center

        Args:
            frame: Input frame (BGR)
            roi_size: Ukuran ROI (default: 100x100)

        Returns:
            list: List of 9 ROI images (grayscale)
        """
        # Convert ke grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape

        # Pastikan ROI tidak melebihi ukuran image
        roi_size = min(roi_size, min(h, w) // 3)
        half_roi = roi_size // 2

        # Definisi 9 titik center untuk ROI
        # Format: (center_y, center_x)
        roi_centers = [
            # Corner points
            (half_roi, half_roi),                    # Top-left
            (half_roi, w - half_roi),                # Top-right
            (h - half_roi, half_roi),                # Bottom-left
            (h - half_roi, w - half_roi),            # Bottom-right

            # Edge center points
            (half_roi, w // 2),                     # Top-center
            (h - half_roi, w // 2),                 # Bottom-center
            (h // 2, half_roi),                     # Left-center
            (h // 2, w - half_roi),                 # Right-center

            # Center point
            (h // 2, w // 2)                       # Center
        ]

        rois = []
        for center_y, center_x in roi_centers:
            # Calculate ROI boundaries
            y1 = max(0, center_y - half_roi)
            y2 = min(h, center_y + half_roi)
            x1 = max(0, center_x - half_roi)
            x2 = min(w, center_x + half_roi)

            # Extract ROI
            roi = gray[y1:y2, x1:x2]

            # Resize ke ukuran standard jika perlu
            if roi.shape[0] != roi_size or roi.shape[1] != roi_size:
                roi = cv2.resize(roi, (roi_size, roi_size))

            rois.append(roi)

        return rois

    def _calculate_roi_sobel_gradient(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Sobel gradient pada 9 ROI
        Parameter optimal: roi_size=100, ksize=1, weights=(0.5, 0.5)
        Threshold range: 0-50, good_threshold=15
        """
        # Parameter optimal untuk method ini
        roi_size = 100
        sobel_ksize = 1
        weight_gx = 0.5
        weight_gy = 0.5

        # Extract 9 ROI
        rois = self._extract_9_point_rois(frame, roi_size)

        total_score = 0.0
        valid_rois = 0

        for roi in rois:
            if roi.size == 0:
                continue

            # Hitung gradient menggunakan Sobel
            gx = cv2.Sobel(roi, cv2.CV_16S, 1, 0, ksize=sobel_ksize)
            gy = cv2.Sobel(roi, cv2.CV_16S, 0, 1, ksize=sobel_ksize)

            # Convert ke absolute values
            abs_gx = cv2.convertScaleAbs(gx)
            abs_gy = cv2.convertScaleAbs(gy)

            # Combine gradients dengan weighted average
            gradient_magnitude = cv2.addWeighted(abs_gx, weight_gx, abs_gy, weight_gy, 0)

            # Accumulate score
            total_score += float(gradient_magnitude.mean())
            valid_rois += 1

        # Return average score dari semua ROI
        return total_score / valid_rois if valid_rois > 0 else 0.0

    def _calculate_roi_laplacian_variance(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Laplacian variance pada 9 ROI
        Parameter optimal: roi_size=100, ksize=3
        Threshold range: 0-1000, good_threshold=100
        """
        # Parameter optimal untuk method ini
        roi_size = 100
        ksize = 3

        # Extract 9 ROI
        rois = self._extract_9_point_rois(frame, roi_size)

        total_score = 0.0
        valid_rois = 0

        for roi in rois:
            if roi.size == 0:
                continue

            # Hitung Laplacian dengan kernel size optimal
            laplacian = cv2.Laplacian(roi, cv2.CV_64F, ksize=ksize)

            # Accumulate score
            total_score += float(laplacian.var())
            valid_rois += 1

        # Return average score dari semua ROI
        return total_score / valid_rois if valid_rois > 0 else 0.0

    def _calculate_roi_brenner_gradient(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Brenner gradient pada 9 ROI
        Parameter optimal: roi_size=100, step=2
        Threshold range: 0-1000000, good_threshold=3000000
        """
        # Parameter optimal untuk method ini
        roi_size = 100
        step = 2

        # Extract 9 ROI
        rois = self._extract_9_point_rois(frame, roi_size)

        total_score = 0.0
        valid_rois = 0

        for roi in rois:
            if roi.size == 0:
                continue

            h, w = roi.shape
            brenner_score = 0.0

            # Horizontal differences dengan step optimal
            if w > step:
                diff_h = roi[:, step:].astype(np.float32) - roi[:, :-step].astype(np.float32)
                brenner_score += np.sum(diff_h ** 2)

            # Vertical differences dengan step optimal
            if h > step:
                diff_v = roi[step:, :].astype(np.float32) - roi[:-step, :].astype(np.float32)
                brenner_score += np.sum(diff_v ** 2)

            # Accumulate score
            total_score += brenner_score
            valid_rois += 1

        # Return average score dari semua ROI
        return total_score / valid_rois if valid_rois > 0 else 0.0

    def _calculate_roi_tenengrad(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Tenengrad pada 9 ROI
        Parameter optimal: roi_size=100, ksize=3, threshold_factor=0.1
        Threshold range: 0-10000000, good_threshold=5000000
        """
        # Parameter optimal untuk method ini
        roi_size = 100
        ksize = 3
        threshold_factor = 0.1

        # Extract 9 ROI
        rois = self._extract_9_point_rois(frame, roi_size)

        total_score = 0.0
        valid_rois = 0

        for roi in rois:
            if roi.size == 0:
                continue

            # Sobel gradients dengan kernel size optimal
            gx = cv2.Sobel(roi, cv2.CV_64F, 1, 0, ksize=ksize)
            gy = cv2.Sobel(roi, cv2.CV_64F, 0, 1, ksize=ksize)

            # Gradient magnitude squared
            gradient_magnitude_sq = gx**2 + gy**2

            # Threshold optimal untuk mengurangi noise
            threshold = np.mean(gradient_magnitude_sq) * threshold_factor
            tenengrad_score = np.sum(gradient_magnitude_sq[gradient_magnitude_sq > threshold])

            # Accumulate score
            total_score += tenengrad_score
            valid_rois += 1

        # Return average score dari semua ROI
        return total_score / valid_rois if valid_rois > 0 else 0.0

    def _calculate_roi_variance_of_laplacian(self, frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Variance of Laplacian pada 9 ROI
        Parameter optimal: roi_size=100, ksize=1
        Threshold range: 0-800, good_threshold=80
        """
        # Parameter optimal untuk method ini
        roi_size = 100
        ksize = 1

        # Extract 9 ROI
        rois = self._extract_9_point_rois(frame, roi_size)

        total_score = 0.0
        valid_rois = 0

        for roi in rois:
            if roi.size == 0:
                continue

            # Laplacian dengan kernel size optimal untuk speed
            laplacian = cv2.Laplacian(roi, cv2.CV_64F, ksize=ksize)

            # Accumulate score
            total_score += float(laplacian.var())
            valid_rois += 1

        # Return average score dari semua ROI
        return total_score / valid_rois if valid_rois > 0 else 0.0

    def get_method_info(self) -> dict:
        """
        Mendapatkan informasi tentang method yang tersedia dan parameter optimal

        Returns:
            dict: Informasi method dan parameter optimal masing-masing
        """
        method_params = {
            'sobel_gradient': {
                'resize_factor': 0.2,
                'sobel_ksize': 1,
                'weights': (0.5, 0.5),
                'description': 'Balanced speed and quality'
            },
            'laplacian_variance': {
                'resize_factor': 0.2,
                'ksize': 3,
                'description': 'Good speed with accuracy'
            },
            'brenner_gradient': {
                'resize_factor': 0.2,
                'step': 2,
                'description': 'High precision, slower'
            },
            'tenengrad': {
                'resize_factor': 0.2,
                'ksize': 3,
                'threshold_factor': 0.1,
                'description': 'Good for noisy images'
            },
            'variance_of_laplacian': {
                'resize_factor': 0.2,
                'ksize': 1,
                'description': 'Maximum speed'
            },
            # ROI-based methods
            'roi_sobel_gradient': {
                'roi_size': 100,
                'sobel_ksize': 1,
                'weights': (0.5, 0.5),
                'roi_points': 9,
                'description': 'ROI-based Sobel gradient (9 points sampling)'
            },
            'roi_laplacian_variance': {
                'roi_size': 100,
                'ksize': 3,
                'roi_points': 9,
                'description': 'ROI-based Laplacian variance (9 points sampling)'
            },
            'roi_brenner_gradient': {
                'roi_size': 100,
                'step': 2,
                'roi_points': 9,
                'description': 'ROI-based Brenner gradient (9 points sampling)'
            },
            'roi_tenengrad': {
                'roi_size': 100,
                'ksize': 3,
                'threshold_factor': 0.1,
                'roi_points': 9,
                'description': 'ROI-based Tenengrad (9 points sampling)'
            },
            'roi_variance_of_laplacian': {
                'roi_size': 100,
                'ksize': 1,
                'roi_points': 9,
                'description': 'ROI-based Variance of Laplacian (9 points sampling)'
            }
        }

        return {
            'current_method': self.current_method,
            'available_methods': self.available_methods,
            'method_parameters': method_params,
            'current_method_params': method_params.get(self.current_method, {})
        }

    def get_method_threshold(self, method_name: str = None) -> dict:
        """
        Mendapatkan threshold information untuk method tertentu

        Args:
            method_name: Nama method (jika None, gunakan current method)

        Returns:
            dict: Threshold information
        """
        if method_name is None:
            method_name = self.current_method

        # Threshold ranges untuk setiap method
        threshold_info = {
            'sobel_gradient': {
                'min_score': 0,
                'max_score': 50,
                'good_threshold': 2,
                'fair_threshold': 1,  # 50% dari good_threshold
                'description': 'Typical range: 5-30'
            },
            'laplacian_variance': {
                'min_score': 0,
                'max_score': 50,
                'good_threshold': 15,
                'fair_threshold': 7.5,
                'description': 'Typical range: 20-500'
            },
            'brenner_gradient': {
                'min_score': 0,
                'max_score': 100000,
                'good_threshold': 10000,
                'fair_threshold': 5000,
                'description': 'Typical range: 1000-50000'
            },
            'tenengrad': {
                'min_score': 0,
                'max_score': 50000,
                'good_threshold': 5000,
                'fair_threshold': 2500,
                'description': 'Typical range: 500-25000'
            },
            'variance_of_laplacian': {
                'min_score': 0,
                'max_score': 800,
                'good_threshold': 80,
                'fair_threshold': 40,
                'description': 'Typical range: 15-400'
            },
            # ROI-based methods (similar thresholds to base methods)
            'roi_sobel_gradient': {
                'min_score': 0,
                'max_score': 25,
                'good_threshold': 9,
                'fair_threshold': 4.5,
                'description': 'ROI-based: Typical range: 5-30'
            },
            'roi_laplacian_variance': {
                'min_score': 0,
                'max_score': 1000,
                'good_threshold': 100,
                'fair_threshold': 50,
                'description': 'ROI-based: Typical range: 20-500'
            },
            'roi_brenner_gradient': {
                'min_score': 0,
                'max_score': 100000,
                'good_threshold': 10000,
                'fair_threshold': 5000,
                'description': 'ROI-based: Typical range: 1000-50000'
            },
            'roi_tenengrad': {
                'min_score': 0,
                'max_score': 50000,
                'good_threshold': 5000,
                'fair_threshold': 2500,
                'description': 'ROI-based: Typical range: 500-25000'
            },
            'roi_variance_of_laplacian': {
                'min_score': 0,
                'max_score': 800,
                'good_threshold': 80,
                'fair_threshold': 40,
                'description': 'ROI-based: Typical range: 15-400'
            }
        }

        return threshold_info.get(method_name, {
            'min_score': 0,
            'max_score': 100,
            'good_threshold': 50,
            'fair_threshold': 25,
            'description': 'Unknown method'
        })

    def evaluate_focus_quality(self, score: float, method_name: str = None) -> dict:
        """
        Evaluasi kualitas focus berdasarkan score dan method

        Args:
            score: Focus score
            method_name: Nama method (jika None, gunakan current method)

        Returns:
            dict: Evaluasi kualitas focus
        """
        threshold_info = self.get_method_threshold(method_name)

        good_threshold = threshold_info['good_threshold']
        fair_threshold = threshold_info['fair_threshold']

        if score >= good_threshold:
            quality = "Good Focus"
            color = "green"
            confidence = min(100, (score / good_threshold) * 100)
        elif score >= fair_threshold:
            quality = "Fair Focus"
            color = "orange"
            confidence = (score / good_threshold) * 100
        else:
            quality = "Poor Focus"
            color = "red"
            confidence = (score / good_threshold) * 100

        # Normalize score ke 0-100
        min_score = threshold_info['min_score']
        max_score = threshold_info['max_score']
        normalized_score = ((score - min_score) / (max_score - min_score)) * 100
        normalized_score = max(0, min(100, normalized_score))

        return {
            'raw_score': score,
            'normalized_score': normalized_score,
            'quality': quality,
            'color': color,
            'confidence': confidence,
            'threshold_info': threshold_info
        }

    def benchmark_methods(self, frame: np.ndarray, iterations: int = 10) -> dict:
        """
        Benchmark semua method yang tersedia

        Args:
            frame: Frame untuk testing
            iterations: Jumlah iterasi untuk benchmark

        Returns:
            dict: Hasil benchmark untuk setiap method
        """
        if frame is None or frame.size == 0:
            return {}

        results = {}
        original_method = self.current_method

        print(f"FocusCalculator: Benchmarking {len(self.available_methods)} methods with {iterations} iterations...")

        for method in self.available_methods:
            self.set_method(method)

            scores = []
            times = []

            for i in range(iterations):
                score, calc_time = self.calculate_focus_score(frame)
                scores.append(score)
                times.append(calc_time)

            results[method] = {
                'avg_score': np.mean(scores),
                'std_score': np.std(scores),
                'avg_time': np.mean(times),
                'std_time': np.std(times),
                'min_time': np.min(times),
                'max_time': np.max(times)
            }

            print(f"  {method}: score={np.mean(scores):.2f}±{np.std(scores):.2f}, "
                  f"time={np.mean(times)*1000:.2f}±{np.std(times)*1000:.2f}ms")

        # Restore original method
        self.set_method(original_method)

        return results

# Convenience function untuk backward compatibility
def calculate_focus_score(frame: np.ndarray, method: str = "sobel_gradient") -> Tuple[float, float]:
    """
    Convenience function untuk menghitung focus score

    Args:
        frame: Frame gambar
        method: Method yang akan digunakan

    Returns:
        Tuple[float, float]: (focus_score, calculation_time)
    """
    calculator = FocusCalculator()
    calculator.set_method(method)
    return calculator.calculate_focus_score(frame)

# Test function
if __name__ == "__main__":
    print("Calculate_AF - Focus Score Calculator")
    print("Available methods:")

    calculator = FocusCalculator()
    for method in calculator.available_methods:
        print(f"  - {method}")

    print(f"\nCurrent method: {calculator.current_method}")
    print("Use FocusCalculator class untuk advanced usage atau calculate_focus_score() function untuk simple usage")
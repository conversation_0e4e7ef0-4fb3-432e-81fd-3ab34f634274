"""
GridCalculator.py - Menghitung Grid Pergerakan untuk Stitching
Menghitung titik-titik grid berdasarkan area ROI dan parameter overlap
"""

from PyQt5.QtCore import QRect, QPoint
from typing import List, Tuple
import math

class GridCalculator:
    def __init__(self):
        """Inisialisasi Grid Calculator"""
        # Asumsi ukuran field of view kamera dalam mm
        # Ini harus disesuaikan dengan kamera dan lensa yang digunakan
        self.camera_fov_width_mm = 5.0   # Lebar FOV kamera dalam mm
        self.camera_fov_height_mm = 3.75  # Tinggi FOV kamera dalam mm
        
        # Resolusi kamera (untuk konversi pixel ke mm)
        self.camera_resolution_width = 1920
        self.camera_resolution_height = 1080
        
        print("GridCalculator: Initialized")
        print(f"Camera FOV: {self.camera_fov_width_mm}x{self.camera_fov_height_mm} mm")
    
    def set_camera_fov(self, width_mm: float, height_mm: float):
        """
        Set ukuran field of view kamera
        
        Args:
            width_mm: Lebar FOV dalam mm
            height_mm: Tinggi FOV dalam mm
        """
        self.camera_fov_width_mm = width_mm
        self.camera_fov_height_mm = height_mm
        print(f"GridCalculator: Camera FOV updated to {width_mm}x{height_mm} mm")
    
    def set_camera_resolution(self, width: int, height: int):
        """
        Set resolusi kamera
        
        Args:
            width: Lebar resolusi dalam pixel
            height: Tinggi resolusi dalam pixel
        """
        self.camera_resolution_width = width
        self.camera_resolution_height = height
        print(f"GridCalculator: Camera resolution updated to {width}x{height} px")
    
    def calculate_grid(self, roi_rect: QRect, move_controller, overlap_percentage: int = 20) -> List[Tuple[float, float]]:
        """
        Menghitung grid points untuk stitching berdasarkan area ROI
        
        Args:
            roi_rect: Area ROI dalam koordinat pixel (gambar asli)
            move_controller: Instance MoveToController untuk konversi koordinat
            overlap_percentage: Persentase overlap antar gambar (0-50%)
            
        Returns:
            List of (grbl_x, grbl_y) coordinates
        """
        if not move_controller:
            print("GridCalculator: MoveToController tidak tersedia")
            return []
        
        print(f"GridCalculator: Calculating grid for ROI {roi_rect}")
        print(f"GridCalculator: Overlap percentage: {overlap_percentage}%")
        
        # Konversi ROI pixel ke ukuran dalam mm
        roi_width_mm, roi_height_mm = self._pixel_roi_to_mm(roi_rect)
        
        print(f"GridCalculator: ROI size in mm: {roi_width_mm:.3f} x {roi_height_mm:.3f}")
        
        # Hitung step size berdasarkan overlap
        step_width_mm = self.camera_fov_width_mm * (1 - overlap_percentage / 100)
        step_height_mm = self.camera_fov_height_mm * (1 - overlap_percentage / 100)
        
        print(f"GridCalculator: Step size: {step_width_mm:.3f} x {step_height_mm:.3f} mm")
        
        # Hitung jumlah grid points
        grid_cols = max(1, math.ceil(roi_width_mm / step_width_mm))
        grid_rows = max(1, math.ceil(roi_height_mm / step_height_mm))
        
        print(f"GridCalculator: Grid size: {grid_cols} x {grid_rows} = {grid_cols * grid_rows} points")
        
        # Generate grid points
        grid_points = []
        
        # Konversi corner ROI ke koordinat Grbl sebagai referensi
        roi_top_left_grbl = move_controller.pixel_to_grbl_coordinates(roi_rect.left(), roi_rect.top())
        roi_bottom_right_grbl = move_controller.pixel_to_grbl_coordinates(roi_rect.right(), roi_rect.bottom())
        
        print(f"GridCalculator: ROI corners in Grbl coordinates:")
        print(f"  Top-left: ({roi_top_left_grbl[0]:.3f}, {roi_top_left_grbl[1]:.3f})")
        print(f"  Bottom-right: ({roi_bottom_right_grbl[0]:.3f}, {roi_bottom_right_grbl[1]:.3f})")
        
        # Generate grid berdasarkan step size dalam koordinat Grbl
        # Menggunakan row-major order untuk konsistensi dengan grid-based stitching
        for row in range(grid_rows):
            for col in range(grid_cols):
                # Hitung offset dari top-left corner
                offset_x_mm = col * step_width_mm
                offset_y_mm = row * step_height_mm

                # Koordinat Grbl untuk titik grid ini
                grbl_x = roi_top_left_grbl[0] + offset_x_mm
                grbl_y = roi_top_left_grbl[1] + offset_y_mm

                # Pastikan masih dalam batas ROI (dengan sedikit toleransi)
                tolerance_mm = 0.1  # 0.1mm toleransi
                if (grbl_x <= roi_bottom_right_grbl[0] + tolerance_mm and
                    grbl_y <= roi_bottom_right_grbl[1] + tolerance_mm):
                    grid_points.append((grbl_x, grbl_y))
        
        print(f"GridCalculator: Generated {len(grid_points)} valid grid points")

        # Debug: print beberapa grid points pertama
        for i, (x, y) in enumerate(grid_points[:5]):
            print(f"  Point {i+1}: ({x:.3f}, {y:.3f})")
        if len(grid_points) > 5:
            print(f"  ... and {len(grid_points) - 5} more points")

        # Simpan grid info untuk stitching
        self.last_grid_info = {
            'grid_cols': grid_cols,
            'grid_rows': grid_rows,
            'step_size_mm': (step_width_mm, step_height_mm),
            'overlap_percentage': overlap_percentage,
            'total_points': len(grid_points)
        }

        return grid_points

    def get_last_grid_info(self) -> dict:
        """
        Mendapatkan informasi grid yang terakhir dihitung

        Returns:
            dict: Informasi grid terakhir
        """
        return getattr(self, 'last_grid_info', {})
    
    def _pixel_roi_to_mm(self, roi_rect: QRect) -> Tuple[float, float]:
        """
        Konversi ukuran ROI dari pixel ke mm
        
        Args:
            roi_rect: Area ROI dalam pixel
            
        Returns:
            Tuple (width_mm, height_mm)
        """
        # Hitung rasio pixel ke mm berdasarkan FOV kamera
        pixel_to_mm_x = self.camera_fov_width_mm / self.camera_resolution_width
        pixel_to_mm_y = self.camera_fov_height_mm / self.camera_resolution_height
        
        # Konversi ukuran ROI ke mm
        roi_width_mm = roi_rect.width() * pixel_to_mm_x
        roi_height_mm = roi_rect.height() * pixel_to_mm_y
        
        return roi_width_mm, roi_height_mm
    
    def calculate_estimated_time(self, grid_points_count: int, capture_delay: float = 1.0, 
                               movement_time: float = 2.0) -> float:
        """
        Estimasi waktu total untuk stitching
        
        Args:
            grid_points_count: Jumlah grid points
            capture_delay: Delay setelah movement sebelum capture (detik)
            movement_time: Estimasi waktu movement antar titik (detik)
            
        Returns:
            Estimasi waktu total dalam detik
        """
        time_per_point = movement_time + capture_delay + 0.5  # 0.5s untuk capture
        total_time = grid_points_count * time_per_point
        
        print(f"GridCalculator: Estimated time for {grid_points_count} points: {total_time:.1f}s ({total_time/60:.1f} min)")
        
        return total_time
    
    def optimize_grid_path(self, grid_points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """
        Optimasi urutan grid points untuk meminimalkan waktu pergerakan
        Menggunakan algoritma sederhana zigzag pattern
        
        Args:
            grid_points: List koordinat grid points
            
        Returns:
            List koordinat grid points yang sudah dioptimasi
        """
        if not grid_points:
            return []
        
        print("GridCalculator: Optimizing grid path...")
        
        # Untuk saat ini, gunakan algoritma sederhana
        # Bisa dikembangkan dengan algoritma TSP untuk optimasi yang lebih baik
        
        # Sort berdasarkan Y terlebih dahulu, kemudian X
        sorted_points = sorted(grid_points, key=lambda p: (p[1], p[0]))
        
        # Implementasi zigzag pattern
        optimized_points = []
        current_row_y = None
        current_row_points = []
        reverse_row = False
        
        for point in sorted_points:
            if current_row_y is None or abs(point[1] - current_row_y) > 0.1:  # New row
                if current_row_points:
                    if reverse_row:
                        current_row_points.reverse()
                    optimized_points.extend(current_row_points)
                    reverse_row = not reverse_row
                
                current_row_y = point[1]
                current_row_points = [point]
            else:
                current_row_points.append(point)
        
        # Add last row
        if current_row_points:
            if reverse_row:
                current_row_points.reverse()
            optimized_points.extend(current_row_points)
        
        print(f"GridCalculator: Path optimized - {len(optimized_points)} points")
        
        return optimized_points
    
    def get_grid_info(self, roi_rect: QRect, overlap_percentage: int = 20) -> dict:
        """
        Mendapatkan informasi grid tanpa generate actual points
        
        Args:
            roi_rect: Area ROI dalam pixel
            overlap_percentage: Persentase overlap
            
        Returns:
            Dictionary dengan informasi grid
        """
        roi_width_mm, roi_height_mm = self._pixel_roi_to_mm(roi_rect)
        
        step_width_mm = self.camera_fov_width_mm * (1 - overlap_percentage / 100)
        step_height_mm = self.camera_fov_height_mm * (1 - overlap_percentage / 100)
        
        grid_cols = max(1, math.ceil(roi_width_mm / step_width_mm))
        grid_rows = max(1, math.ceil(roi_height_mm / step_height_mm))
        total_points = grid_cols * grid_rows
        
        estimated_time = self.calculate_estimated_time(total_points)
        
        return {
            'roi_size_mm': (roi_width_mm, roi_height_mm),
            'step_size_mm': (step_width_mm, step_height_mm),
            'grid_size': (grid_cols, grid_rows),
            'total_points': total_points,
            'estimated_time_seconds': estimated_time,
            'estimated_time_minutes': estimated_time / 60,
            'overlap_percentage': overlap_percentage
        }

# Test function
if __name__ == "__main__":
    # Test grid calculator
    calculator = GridCalculator()
    
    # Test ROI
    test_roi = QRect(100, 100, 800, 600)  # Example ROI
    
    # Get grid info
    info = calculator.get_grid_info(test_roi, 20)
    print("Grid Info:")
    for key, value in info.items():
        print(f"  {key}: {value}")

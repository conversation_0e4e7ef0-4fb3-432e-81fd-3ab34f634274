# File: Constant.py
"""
File berisi nilai-nilai konfigurasi yang digunakan lintas modul.
Sekarang di-softcode melalui configuration.AppConfig sehingga perubahan nilai
dapat dilakukan dari file JSON tanpa mengubah kode sumber.
"""

from PyQt5.QtCore import Qt
from configuration import config

# --------------------------------------------------------------------------------------
# Helper builders (konversi dari config JSON ke objek yang dipakai kode lama)
# --------------------------------------------------------------------------------------

def _build_valid_keys():
    """
    Membangun peta tombol yang valid dari konfigurasi:
    config key: jogging.valid_keys
    Format JSON:
      "valid_keys": {
        "Key_Up": ["Y", 65],
        "Key_Down": ["Y", 0],
        ...
      }
    Dikembalikan dalam format lama:
      { Qt.Key_Up: ("Y", 65), ... }
    """
    result = {}
    table = config.get("jogging.valid_keys", {})
    for key_name, pair in table.items():
        qt_key = getattr(Qt, key_name, None)
        if qt_key is None:
            continue
        if isinstance(pair, (list, tuple)) and len(pair) == 2:
            axis, value = pair
            result[qt_key] = (axis, value)
    return result


def _icon_path(name: str) -> str:
    """
    Menghasilkan path icon relatif (misal 'Icons/up.png') dari konfigurasi.
    """
    return config.icon_path(name)


# --------------------------------------------------------------------------------------
# Nilai-nilai yang diekspos (kompatibel dengan kode existing)
# --------------------------------------------------------------------------------------

# Pemetaan tombol jog
valid_keys = _build_valid_keys()

# Daftar ukuran zoom yang tersedia
zoom_size = config.camera_zoom_levels()

# Path Icon untuk UI (tetap kompatibel dengan pemanggil lama)
Icon_up = _icon_path("up")
Icon_down = _icon_path("down")
Icon_left = _icon_path("left")
Icon_right = _icon_path("right")
Icon_zup = _icon_path("zup")
Icon_zdown = _icon_path("zdown")
Icon_next = _icon_path("next")

# Path untuk FFC (nama file; direktori aktual dikelola oleh ScaleAdjust)
_ffc_map = config.get("camera.ffc_files", {})
ffc_4 = _ffc_map.get("4X", "ffc_4X.ffc")
ffc_10 = _ffc_map.get("10X", "ffc_10X.ffc")
ffc_40 = _ffc_map.get("20X", "ffc_40X.ffc")  # mengikuti pemetaan asli
ffc_100 = _ffc_map.get("40X", "ffc_100X.ffc")
ffc_prev = "Preview.ffc"

# Pengaturan kamera per zoom (struktur dipertahankan untuk kompatibilitas)
ZOOM_SETTINGS = config.zoom_settings()

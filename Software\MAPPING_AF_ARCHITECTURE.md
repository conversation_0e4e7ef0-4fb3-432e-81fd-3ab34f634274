# Mapping AF Architecture - Safe and Stable Implementation

## Problem Analysis

### Original Issues:
1. **Thread Conflicts**: MappingAFWorker tried to use UI's autofocus_worker from different threads
2. **Signal Connection Problems**: request_autofocus connected to UI methods that create new threads
3. **Blocking Waits**: `while self.main_app.autofocus_worker.is_running: time.sleep(0.1)` 
4. **Thread Safety**: Direct access to worker properties without synchronization
5. **Resource Conflicts**: Multiple autofocus processes could run simultaneously

## New Safe Architecture

### 1. Dedicated AutoFocus Worker
```python
class MappingAFWorker(QObject):
    def __init__(self, camera, grbl, grbl_start, grbl_end):
        # Create dedicated autofocus worker for mapping
        self.autofocus_worker = AutoFocusWorker(camera, grbl)
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished)
```

### 2. Event-Driven Processing
```python
def process_next_point(self):
    """Process points sequentially using event-driven approach"""
    if self.current_point >= len(self.points_queue):
        self.finished.emit()
        return
    
    x, y, af_type = self.points_queue[self.current_point]
    
    # Move to position
    self.grbl.move_to(x, y)
    self.grbl.wait_for_idle()
    
    # Start autofocus (non-blocking)
    if af_type == 'full':
        self.autofocus_worker.run_autofocus()
    else:
        self.autofocus_worker.run_refinement_autofocus()

@pyqtSlot(float, float)
def on_autofocus_finished(self, best_pos, best_score):
    """Called when autofocus finishes - process next point"""
    self.results[(x, y)] = best_pos
    self.current_point += 1
    self.process_next_point()  # Continue to next point
```

### 3. Thread Safety
```python
# Each MappingAFWorker has its own thread with dedicated resources
self.mapping_af_worker.moveToThread(self.mapping_af_thread)
self.mapping_af_worker.autofocus_worker.moveToThread(self.mapping_af_thread)

# No shared resources between UI autofocus and mapping autofocus
```

### 4. Conflict Prevention
```python
def on_autofocus_clicked(self):
    # Prevent starting autofocus if mapping is running
    if self.mapping_af_thread and self.mapping_af_thread.isRunning():
        QMessageBox.warning(self, "Error", "Mapping AF sedang berjalan...")
        return

def on_mapping_af_clicked(self):
    # Prevent starting mapping if autofocus is running
    if self.autofocus_thread and self.autofocus_thread.isRunning():
        QMessageBox.warning(self, "Error", "Auto Focus sedang berjalan...")
        return
```

## Process Flow

### 1. Initialization
```
User clicks "Mapping AF"
→ Check no other AF process running
→ Create MappingAFWorker with dedicated AutoFocusWorker
→ Move both to separate thread
→ Start mapping process
```

### 2. Smart Point Processing Order
```
CRITICAL: Process CENTER point FIRST to establish baseline Z!

Queue Order:
1. CENTER (1,1) - FULL AF - Establishes baseline Z from unknown position
2. Point (0,0) - REFINE AF - Uses baseline Z for faster convergence
3. Point (0,1) - REFINE AF
4. Point (0,2) - REFINE AF
5. Point (1,0) - REFINE AF
6. Point (1,2) - REFINE AF
7. Point (2,0) - REFINE AF
8. Point (2,1) - REFINE AF
9. Point (2,2) - REFINE AF

Why CENTER first?
- Initial Z position might be far from focus (e.g., Z=5 when focus is at Z=10)
- FULL AF does coarse scan (Z=9 to Z=11) then fine scan to find optimal Z
- REFINE AF only does small adjustments around current Z
- If we start with REFINE at wrong Z, it will fail to find focus
```

### 3. Point Processing Details
```
For each point in optimized order:
1. Move GRBL to position (x, y)
2. Wait for movement completion
3. Start autofocus:
   - FULL AF for center: Coarse + Fine scan
   - REFINE AF for others: Small adjustments around baseline
4. Wait for autofocus completion via signal
5. Store result with detailed logging
6. Process next point
```

### 3. Completion
```
All points processed
→ Log results
→ Emit finished signal
→ Clean up thread
→ Re-enable UI controls
```

## Safety Features

### 1. Resource Isolation
- Each process has its own AutoFocusWorker instance
- No shared state between UI and mapping autofocus
- Separate threads prevent blocking

### 2. Conflict Prevention
- Mutex-like checking prevents simultaneous processes
- Clear error messages guide user behavior
- Graceful handling of stop requests

### 3. Error Handling
```python
try:
    # Process point
    self.process_next_point()
except Exception as e:
    self.log_message.emit(f"Error: {e}")
    self.current_point += 1
    self.process_next_point()  # Continue with next point
```

### 4. Clean Shutdown
```python
def stop(self):
    self.is_running = False
    if self.autofocus_worker and self.autofocus_worker.is_running:
        self.autofocus_worker.stop_autofocus()
```

## Benefits

1. **No Thread Conflicts**: Each process runs in isolation
2. **Non-Blocking**: Event-driven approach prevents UI freezing
3. **Safe Resource Access**: No direct cross-thread property access
4. **Robust Error Handling**: Continues processing even if one point fails
5. **Clean Architecture**: Clear separation of concerns
6. **User-Friendly**: Clear feedback and conflict prevention

## Testing Checklist

- [ ] Mapping AF runs through all 9 points
- [ ] GRBL moves to each position correctly
- [ ] Autofocus executes at each point
- [ ] Results are stored correctly
- [ ] UI remains responsive during process
- [ ] Can cancel mapping AF safely
- [ ] Cannot start conflicting processes
- [ ] Error handling works correctly
- [ ] Thread cleanup happens properly
- [ ] Log messages provide clear feedback

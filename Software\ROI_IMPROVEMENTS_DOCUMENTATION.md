# ROI Functionality Improvements

## Masalah yang Diperbaiki

### 1. Struktur Label yang Salah
**Masalah Sebelumnya:**
- Salah memahami requirement: ROI harus bisa dipilih di main label (yang besar)
- `lbl_live_preview_popup` adalah nama teknis untuk preview stream, bukan popup label
- ROI selection di tempat yang salah

**Solusi:**
- `lbl_video` (main label/yang besar) sekarang adalah `ROILabel`
- `lbl_live_preview_popup` (popup label/yang kecil) adalah `QLabel` biasa
- ROI selection dilakukan di main label ketika preview stream ada di sana

### 2. Logika Swap yang Tidak Jelas
**Masalah Sebelumnya:**
- Swap hanya mengubah stream tanpa logika ROI yang jelas
- R<PERSON><PERSON> controls diaktifkan/dinonaktifkan tanpa penjelasan yang tepat

**Solusi:**
- Clarifikasi bahwa swap menukar stream video, bukan posisi fisik label
- <PERSON><PERSON><PERSON> controls hanya aktif ketika main camera feed ditampilkan di `lbl_live_preview_popup` (ROILabel)
- Dokumentasi yang jelas tentang cara kerja sistem

### 3. Konflik Mouse Event Handling
**Masalah Sebelumnya:**
- `mousePressEvent` dari ROILabel di-override untuk swap functionality
- ROI selection tidak berfungsi karena mouse events tidak sampai ke ROILabel
- User tidak bisa drag untuk select ROI setelah swap

**Solusi:**
- Implementasi smart mouse event handling yang memilih antara ROI selection atau swap
- Ketika `is_swapped=True`: mouse events diteruskan ke ROILabel untuk ROI selection
- Ketika `is_swapped=False`: mouse events digunakan untuk swap functionality

### 4. Fungsi ROI Tidak Konsisten
**Masalah Sebelumnya:**
- Crop ROI tidak berfungsi
- Mapping AF menampilkan warning "silahkan pilih area ROI"
- Fungsi ROI bergantung pada label yang aktif secara dinamis

**Solusi:**
- Semua fungsi ROI (`crop_roi`, `reset_roi`, `display_roi_info`, `on_mapping_af_clicked`) selalu menggunakan `lbl_live_preview_popup`
- Validasi ROI yang lebih baik sebelum eksekusi
- Error handling yang lebih informatif

## Perubahan Kode Detail

### 1. Perubahan Struktur Label (`_create_live_stream_tab`)
```python
# Struktur yang benar:
self.lbl_video = ROILabel(video_container)  # Main label (besar) - bisa ROI
self.lbl_live_preview_popup = QLabel(video_container)  # Popup label (kecil) - hanya swap
```

### 2. Fungsi Helper untuk ROI Label Aktif
```python
def _get_active_roi_label(self):
    """
    Mengembalikan ROI label yang aktif berdasarkan kondisi swap.
    ROI hanya bisa dilakukan di main label ketika preview stream ada di sana.
    """
    if self.preview_driver.is_swapped:
        return self.lbl_video  # Preview stream ada di main label
    else:
        return None  # Main stream ada di main label, tidak bisa ROI
```

### 3. Clarifikasi Logika Swap Stream
```python
def _handle_stream_swap(self):
    """
    Menangani swap stream antara main camera dan preview camera.

    Cara kerja:
    - Label tetap di posisi fisik yang sama
    - Yang berubah adalah stream mana yang ditampilkan di label mana
    - Ketika is_swapped=True: main camera feed → lbl_live_preview_popup
    - ROI controls aktif karena main camera feed sekarang di ROILabel
    """
```

### 3. Kontrol ROI yang Konsisten
```python
def _handle_stream_swap(self):
    is_roi_active = self.preview_driver.is_swapped
    self.roi_set.setEnabled(is_roi_active)
    self.roi_crop.setEnabled(is_roi_active)
    self.roi_reset.setEnabled(is_roi_active)
    self.roi_move.setEnabled(is_roi_active)
    self.btn_mapping_af.setEnabled(is_roi_active)
```

### 4. Fungsi ROI yang Diperbaiki
```python
def crop_roi(self):
    """Crop ROI - selalu menggunakan lbl_live_preview_popup sebagai target."""
    if not self.lbl_live_preview_popup.roi_rect.isValid():
        QMessageBox.warning(self, "Error", "Silakan pilih area ROI terlebih dahulu.")
        return
    # ... rest of implementation
```

## Cara Kerja Sistem Baru

### 1. Kondisi Awal
- `lbl_video` (main label/besar) - menampilkan main camera feed
- `lbl_live_preview_popup` (popup label/kecil) - menampilkan preview camera feed
- ROI controls nonaktif (karena main camera feed di main label, tidak bisa ROI)

### 2. Setelah Swap (Klik Popup)
- `lbl_video` (main label/besar) - sekarang menampilkan preview camera feed
- `lbl_live_preview_popup` (popup label/kecil) - sekarang menampilkan main camera feed
- ROI controls aktif (karena preview camera feed sekarang di main label yang bisa ROI)
- User bisa memilih ROI, crop, mapping AF, dll. pada preview camera feed di main label

### 3. Swap Kembali
- Stream kembali ke kondisi awal
- ROI controls nonaktif lagi

## Keuntungan Sistem Baru

1. **Konsistensi**: Semua fungsi ROI selalu menggunakan `lbl_live_preview_popup`
2. **Intuitive**: ROI controls hanya aktif ketika main camera feed ditampilkan di ROILabel
3. **Reliable**: Validasi yang lebih baik mencegah error
4. **Clear Separation**: `lbl_video` untuk display saja, `lbl_live_preview_popup` untuk ROI
5. **Stable UI**: Label tetap di posisi fisik yang sama, tidak ada perubahan layout yang merusak

## Testing

Gunakan `test_roi_functionality.py` untuk memverifikasi:
1. Struktur label yang benar
2. Status awal ROI controls
3. Fungsi swap yang benar
4. Manual testing untuk semua fitur ROI

## Fitur yang Sekarang Berfungsi

✅ Swap stream antara main camera dan preview camera
✅ ROI selection hanya pada ROILabel (lbl_live_preview_popup)
✅ Crop ROI berfungsi
✅ Reset ROI berfungsi
✅ Mapping AF berfungsi dengan ROI
✅ Move To berfungsi
✅ Kontrol ROI yang konsisten
✅ UI tetap stabil tanpa perubahan layout yang merusak

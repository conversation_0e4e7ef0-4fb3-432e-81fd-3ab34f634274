# File: focus_plot_window.py

from PyQt5.QtWidgets import QDialog, QVBoxLayout
from PyQt5.QtCore import pyqtSlot
import pyqtgraph as pg
from scipy.interpolate import make_interp_spline
import numpy as np

# Atur tema default pyqtgraph agar enak dilihat
pg.setConfigOption('background', 'w')
pg.setConfigOption('foreground', 'k')

class FocusPlotWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Analisis Hasil Auto Focus")
        self.resize(800, 700)
        
        # Layout utama
        layout = QVBoxLayout(self)

        # --- Widget Plot Pertama: Kurva Fokus ---
        self.focus_plot = pg.PlotWidget()
        self.focus_plot.setTitle("Kurva Fokus (Z vs. Skor)")
        self.focus_plot.setLabel('left', 'Skor Fokus')
        self.focus_plot.setLabel('bottom', 'Posisi Z (mm)')
        self.focus_plot.showGrid(x=True, y=True)

        # Coarse raw
        self.focus_raw_plot = self.focus_plot.plot([], [], pen=None, symbol='o',
                                                   symbolBrush=(100, 100, 255, 150), symbolSize=5, name="Coarse Raw")
        # Coarse spline
        self.focus_smooth_plot = self.focus_plot.plot([], [], pen=pg.mkPen('r', width=2), name="Coarse Spline")

        # Fine raw
        self.focus_fine_plot_raw = self.focus_plot.plot([], [], pen=None, symbol='x',
                                                        symbolBrush='g', symbolSize=6, name="Fine Raw")
        # Fine spline
        self.focus_fine_plot_smooth = self.focus_plot.plot([], [], pen=pg.mkPen('m', style=pg.QtCore.Qt.DashLine, width=2),
                                                           name="Fine Spline")

        # --- Widget Plot Kedua: Waktu Kalkulasi ---
        self.time_plot = pg.PlotWidget()
        self.time_plot.setTitle("Performa Kalkulasi Skor")
        self.time_plot.setLabel('left', 'Waktu (ms)')
        self.time_plot.setLabel('bottom', 'Nomor Frame')
        self.time_plot.showGrid(x=True, y=True)

        # Coarse
        self.time_series_plot = self.time_plot.plot([], [], pen=pg.mkPen('b', width=2), name="Coarse Time")
        # Fine
        self.time_series_fine_plot = self.time_plot.plot([], [], pen=pg.mkPen('g', style=pg.QtCore.Qt.DashLine, width=2),
                                                         name="Fine Time")

        # Tambahkan kedua plot ke layout
        layout.addWidget(self.focus_plot)
        layout.addWidget(self.time_plot)

    @pyqtSlot(dict, dict)
    def update_plots(self, AF_data, fine_data):
        """Menerima semua data dan menggambar grafik coarse dan fine focus."""
        # Coarse Scan Data
        z_data = AF_data.get("z_positions", [])
        score_data = AF_data.get("focus_scores", [])
        time_data = AF_data.get("calc_times", [])

        # Fine Scan Data
        z_data_fine = fine_data.get("z_positions", [])
        score_data_fine = fine_data.get("focus_scores", [])
        time_data_fine = fine_data.get("calc_times", [])

        # Cek ada datanya
        if not z_data or not score_data:
            return

        # === 1. Grafik Kurva Fokus (Coarse + Fine) ===
        # Coarse (raw)
        self.focus_raw_plot.setData(z_data, score_data)

        # Fine (raw)
        if hasattr(self, "focus_fine_plot_raw"):
            self.focus_fine_plot_raw.setData(z_data_fine, score_data_fine)

        # Coarse (spline)
        if len(z_data) > 3:
            try:
                x_np, y_np = np.array(z_data), np.array(score_data)
                xnew = np.linspace(x_np.min(), x_np.max(), 300)
                spl = make_interp_spline(x_np, y_np, k=3)
                power_smooth = spl(xnew)
                self.focus_smooth_plot.setData(xnew, power_smooth)
            except Exception as e:
                print(f"Gagal membuat spline kurva fokus: {e}")
                self.focus_smooth_plot.clear()
        else:
            self.focus_smooth_plot.clear()

        if len(z_data_fine) > 3:
            try:
                x_np_f = np.array(z_data_fine, dtype=float).flatten()
                y_np_f = np.array(score_data_fine, dtype=float).flatten()

                # Urutkan berdasarkan Z (agar strictly increasing)
                sorted_indices = np.argsort(x_np_f)
                x_np_f = x_np_f[sorted_indices]
                y_np_f = y_np_f[sorted_indices]

                # Pastikan ada cukup titik unik
                if len(np.unique(x_np_f)) < 4:
                    raise ValueError("Tidak cukup nilai Z unik untuk spline.")

                # Buat spline
                xnew_f = np.linspace(x_np_f.min(), x_np_f.max(), 300)
                spl_f = make_interp_spline(x_np_f, y_np_f, k=3)
                power_smooth_f = spl_f(xnew_f)
                self.focus_fine_plot_smooth.setData(xnew_f, power_smooth_f)

            except Exception as e:
                print(f"Gagal spline kurva fokus fine: {e}")
                self.focus_fine_plot_smooth.clear()
        else:
            self.focus_fine_plot_smooth.clear()

        # === 2. Grafik Waktu Kalkulasi (Coarse + Fine) ===
        if time_data:
            frame_numbers = list(range(len(time_data)))
            self.time_series_plot.setData(frame_numbers, time_data)

        if time_data_fine and hasattr(self, "time_series_fine_plot"):
            frame_numbers_fine = list(range(len(time_data_fine)))
            self.time_series_fine_plot.setData(frame_numbers_fine, time_data_fine)

"""
Example usage of Z_Interpolation class
This demonstrates how to use the interpolation after MappingAF is complete
"""

import numpy as np
from Z_Interpolation import ZInterpolation

def example_usage():
    """
    Example showing how to use Z interpolation with MappingAF results
    """
    
    # Example: Simulate MappingAF results
    # In real usage, this would come from MappingAFWorker.results
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Simulate 3x3 mapping results (normally from MappingAF)
    # Grid layout:
    # 1 2 3
    # 4 5 6
    # 7 8 9
    mapping_results = {
        (0.0, 0.0): 10.1234,  # Point 1 (top-left)
        (1.5, 0.0): 10.2156,  # Point 2 (top-center)  
        (3.0, 0.0): 10.1987,  # Point 3 (top-right)
        (0.0, 1.5): 10.0876,  # Point 4 (middle-left)
        (1.5, 1.5): 10.1543,  # Point 5 (center)
        (3.0, 1.5): 10.2234,  # Point 6 (middle-right)
        (0.0, 3.0): 10.0654,  # Point 7 (bottom-left)
        (1.5, 3.0): 10.1321,  # Point 8 (bottom-center)
        (3.0, 3.0): 10.1876   # Point 9 (bottom-right)
    }
    
    print("="*60)
    print("Z INTERPOLATION EXAMPLE")
    print("="*60)
    
    # Create interpolation object
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    
    # Validate and create interpolation grid
    print("Step 1: Creating 6x6 interpolation grid...")
    if z_interp.create_interpolation_grid():
        print("✓ Interpolation successful!")
        
        # Display results
        print("\nStep 2: Displaying interpolation results...")
        print(z_interp.log_interpolation_results())
        
        # Example: Get Z value at specific position
        print("\nStep 3: Getting Z values at specific positions...")
        test_positions = [
            (0.5, 0.5),   # Between grid points
            (1.0, 1.0),   # Between grid points
            (2.5, 2.5),   # Between grid points
            (1.5, 1.5),   # Exact original point (center)
            (0.0, 0.0),   # Exact original point (corner)
        ]
        
        for x, y in test_positions:
            z = z_interp.get_z_at_position(x, y)
            if z is not None:
                print(f"  Position ({x:.1f}, {y:.1f}) -> Z = {z:.4f}")
            else:
                print(f"  Position ({x:.1f}, {y:.1f}) -> Z = ERROR")
        
        # Get all interpolated points
        print("\nStep 4: Getting all 6x6 interpolated points...")
        all_points = z_interp.get_interpolated_grid_points()
        print(f"Total interpolated points: {len(all_points)}")
        
        # Show some examples
        print("\nSample interpolated points:")
        count = 0
        for (x, y), z in all_points.items():
            if count < 10:  # Show first 10 points
                print(f"  ({x:.3f}, {y:.3f}) -> Z = {z:.4f}")
                count += 1
            else:
                break
        print(f"  ... and {len(all_points) - 10} more points")
        
        # Save results to file
        print("\nStep 5: Saving interpolation data...")
        z_interp.save_interpolation_data("example_interpolation.txt")
        
    else:
        print("❌ Interpolation failed!")

def integration_example():
    """
    Example showing how to integrate with MappingAFWorker
    """
    print("\n" + "="*60)
    print("INTEGRATION WITH MAPPING AF EXAMPLE")
    print("="*60)
    
    # This is how you would use it after MappingAF completes:
    
    print("""
# After MappingAFWorker finishes, you can use the results like this:

def on_mapping_af_finished(mapping_worker):
    # Get the mapping results
    grbl_start = mapping_worker.grbl_start
    grbl_end = mapping_worker.grbl_end
    mapping_results = mapping_worker.results
    
    # Create interpolation
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    
    # Generate 6x6 interpolated grid
    if z_interp.create_interpolation_grid():
        print("✓ Z interpolation complete!")
        
        # Now you can use interpolated Z values for any position
        # within the ROI for more precise auto-focus
        
        # Example: Get Z for camera movement
        camera_x, camera_y = get_current_camera_position()
        optimal_z = z_interp.get_z_at_position(camera_x, camera_y)
        
        if optimal_z:
            # Move Z axis to interpolated position before fine AF
            grbl.move_z(optimal_z)
            print(f"Moved to interpolated Z: {optimal_z:.4f}")
        
        # Save interpolation data for later use
        z_interp.save_interpolation_data()
        
    else:
        print("❌ Interpolation failed")

# Connect to MappingAF finished signal:
# mapping_worker.finished.connect(lambda: on_mapping_af_finished(mapping_worker))
    """)

def demonstrate_grid_transformation():
    """
    Demonstrate the grid transformation from 3x3 to 6x6
    """
    print("\n" + "="*60)
    print("GRID TRANSFORMATION DEMONSTRATION")
    print("="*60)
    
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Original 3x3 grid
    x_orig = np.linspace(grbl_start[0], grbl_end[0], 3)
    y_orig = np.linspace(grbl_start[1], grbl_end[1], 3)
    
    # New 6x6 grid  
    x_new = np.linspace(grbl_start[0], grbl_end[0], 6)
    y_new = np.linspace(grbl_start[1], grbl_end[1], 6)
    
    print("Original 3x3 grid points:")
    print("X coordinates:", [f"{x:.1f}" for x in x_orig])
    print("Y coordinates:", [f"{y:.1f}" for y in y_orig])
    
    print("\nNew 6x6 interpolated grid points:")
    print("X coordinates:", [f"{x:.1f}" for x in x_new])
    print("Y coordinates:", [f"{y:.1f}" for y in y_new])
    
    print(f"\nGrid spacing:")
    print(f"Original: {x_orig[1] - x_orig[0]:.1f} units")
    print(f"Interpolated: {x_new[1] - x_new[0]:.1f} units")
    print(f"Resolution improvement: {(x_orig[1] - x_orig[0]) / (x_new[1] - x_new[0]):.1f}x")

if __name__ == "__main__":
    # Run all examples
    example_usage()
    integration_example()
    demonstrate_grid_transformation()
    
    print("\n" + "="*60)
    print("EXAMPLE COMPLETE")
    print("="*60)

# Mapping AF Troubleshooting Guide

## Problem Analysis from Log

### Original Issues:
```
Focus Points (Z-position):
  (32.30, 3.63) -> Z=9.0399 |  (36.00, 3.63) -> Z=0.0000 |  (39.70, 3.63) -> Z=0.0000
  (32.30, 6.17) -> Z=0.0000 |  (36.00, 6.17) -> Z=9.0399 |  (39.70, 6.17) -> Z=0.0000
  (32.30, 8.71) -> Z=0.0000 |  (36.00, 8.71) -> Z=0.0000 |  (39.70, 8.71) -> Z=0.0000
```

### Problems Identified:
1. **Z=0.0000 at many points** - AutoFocus failed or didn't run
2. **Fine scan overshooting focus** - Algorithm not converging properly
3. **Refine moving to Z=0** - No safety limits for movement range
4. **Final position X,Y,Z = 0,0,0** - Position not updated after completion

## Fixes Implemented

### 1. AutoFocus Signal Validation
```python
# Before: Duplicate emit and no validation
self.grbl.moveToThread(QApplication.instance().thread())
self.focus_finished.emit(final_pos, final_score)
# ... more code ...
self.focus_finished.emit(final_pos, final_score)  # DUPLICATE!

# After: Single emit with validation
if final_pos > 0:  # Validate that final_pos is valid
    print(f"[AF] Emitting focus_finished: Z={final_pos:.4f}, Score={final_score:.2f}")
    self.focus_finished.emit(final_pos, final_score)
else:
    print(f"[AF ERROR] Invalid final_pos={final_pos}, not emitting signal")
    self.focus_finished.emit(0.0, 0.0)  # Emit default values
```

### 2. Refinement Safety Limits
```python
# Before: Undefined Z_LIMIT_MIN, Z_LIMIT_MAX causing Z=0
end_z = max(Z_LIMIT_MIN, min(Z_LIMIT_MAX, end_z))

# After: Dynamic safety limits based on current position
Z_LIMIT_MIN = max(0.5, z0 - 2.0)  # Don't go below 0.5mm or 2mm below current
Z_LIMIT_MAX = z0 + 2.0  # Don't go more than 2mm above current
end_z = max(Z_LIMIT_MIN, min(Z_LIMIT_MAX, end_z))
```

### 3. Enhanced Logging and Validation
```python
@pyqtSlot(float, float)
def on_autofocus_finished(self, best_pos, best_score):
    self.log_message.emit(f"[MAPPING] AF finished signal received: Z={best_pos:.4f}, Score={best_score:.2f}")
    
    # Validate result
    if best_pos <= 0:
        self.log_message.emit(f"⚠️ WARNING: Invalid Z position {best_pos:.4f} - AF may have failed")
        self.results[(x, y)] = 0.0  # Store as failed
    else:
        self.results[(x, y)] = best_pos
```

### 4. Position Update After Completion
```python
def on_mapping_af_finished(self):
    # Update current position display
    try:
        current_x, current_y, current_z = self.grbl.get_current_position()
        self.update_position_display(current_x, current_y, current_z)
        print(f"[UI] Position updated: X={current_x:.3f}, Y={current_y:.3f}, Z={current_z:.3f}")
    except Exception as e:
        print(f"[UI] Error updating position: {e}")
```

## Expected Behavior After Fixes

### 1. Center Point (Full AF)
```
Starting FULL autofocus at CENTER (36.00, 6.17) - Establishing baseline Z
[AF] Coarse scan: Z=9.000 → Z=10.500
[AF] Fine scan: Z=10.123 → Z=10.223
✓ CENTER AF finished: Z=10.1234, Score=95.67 - BASELINE ESTABLISHED
```

### 2. Other Points (Refine AF)
```
Starting REFINE autofocus at (32.30, 3.63) - Using baseline Z
[AF REFINE] Safety limits: Z_MIN=8.123, Z_MAX=12.123
[AF REFINE] Planned sweep: Z=10.123 → Z=10.223
✓ Refine AF finished at (32.30, 3.63): Z=10.0987, Score=92.34
```

### 3. Final Result
```
Focus Points (Z-position):
  (32.30, 3.63) -> Z=10.0987 |  (36.00, 3.63) -> Z=10.1156 |  (39.70, 3.63) -> Z=10.1298
  (32.30, 6.17) -> Z=10.0876 |  (36.00, 6.17) -> Z=10.1234 |  (39.70, 6.17) -> Z=10.1345
  (32.30, 8.71) -> Z=10.0765 |  (36.00, 8.71) -> Z=10.1123 |  (39.70, 8.71) -> Z=10.1456

[MAPPING] Final position: X=39.70, Y=8.71, Z=10.1456
```

## Debugging Steps

### 1. Check AutoFocus Logs
Look for these patterns:
- `[AF] Emitting focus_finished: Z=X.XXXX, Score=XX.XX` - Good
- `[AF ERROR] Invalid final_pos=0.0000` - AutoFocus failed
- `[AF REFINE] Safety limits: Z_MIN=X.XXX, Z_MAX=X.XXX` - Safety working

### 2. Check Mapping Logs
Look for these patterns:
- `✓ CENTER AF finished: Z=X.XXXX - BASELINE ESTABLISHED` - Good
- `❌ CENTER AF FAILED: Z=0.0000` - Center AF failed
- `⚠️ WARNING: Invalid Z position` - AF returned invalid result

### 3. Check Position Updates
Look for:
- `[UI] Position updated after Mapping AF: X=XX.XX, Y=XX.XX, Z=XX.XX` - Good
- `[UI] Error updating position` - Position update failed

## Common Issues and Solutions

### Issue: Z=0.0000 at multiple points
**Cause**: AutoFocus not running or failing
**Solution**: Check camera connection, lighting, focus algorithm parameters

### Issue: Refine AF goes to extreme Z values
**Cause**: No safety limits in refinement
**Solution**: ✅ Fixed with dynamic safety limits

### Issue: Final position shows 0,0,0
**Cause**: Position not updated after mapping
**Solution**: ✅ Fixed with position update in on_mapping_af_finished

### Issue: Fine scan overshoots focus
**Cause**: Algorithm parameters or patience settings
**Solution**: Adjust fine scan range and patience in config

## Configuration Tuning

### AutoFocus Parameters
```python
# In configuration.py
"autofocus.coarse.start_z": 9.0,
"autofocus.coarse.end_z": 10.5,
"autofocus.fine.range_mm": 0.1,
"autofocus.fine.feed_rate_mmpm": 1.0,
```

### Safety Limits
```python
# In AutoFocus_Worker.py
Z_LIMIT_MIN = max(0.5, z0 - 2.0)  # Adjust -2.0 as needed
Z_LIMIT_MAX = z0 + 2.0            # Adjust +2.0 as needed
```

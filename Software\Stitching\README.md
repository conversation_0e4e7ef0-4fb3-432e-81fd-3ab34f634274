# Stitching System - Slide Scanner

Sistem stitching untuk menggabungkan beberapa gambar dari area ROI yang dipilih menjadi satu gambar besar dengan resolusi tinggi.

## Overview

Sistem ini mengintegrasikan:
- **ROI Selection** dari Roi<PERSON>l.py
- **Movement Control** dari Grbl system
- **Image Capture** dari <PERSON>cam SDK
- **Image Stitching** menggunakan OpenCV

## Workflow

1. **ROI Selection**: User memilih area ROI yang akan di-stitch
2. **Grid Calculation**: Sistem menghitung grid pergerakan dalam area ROI
3. **Movement & Capture**: Grbl bergerak ke setiap titik grid dan mengambil gambar
4. **Image Stitching**: Menggabungkan semua gambar menjadi satu gambar besar
5. **Result Management**: Menyimpan dan menampilkan hasil stitching

## File Structure

```
Software/Stitching/
├── __init__.py              # Package initialization
├── Stitching.py             # Main controller
├── GridCalculator.py        # Grid calculation logic
├── StitchingWorker.py       # Background worker thread
├── ImageStitcher.py         # Image stitching algorithms
├── StitchingUI.py           # UI components
└── README.md               # This file
```

## Components

### 1. StitchingController (Stitching.py)
Main controller yang mengkoordinasi seluruh proses stitching.

**Key Features:**
- Menerima area ROI dari user
- Mengatur parameter stitching (overlap, delay, dll)
- Mengkoordinasi movement dan capture
- Mengelola hasil stitching

**Signals:**
- `stitching_started`: Proses stitching dimulai
- `stitching_progress(current, total, status)`: Update progress
- `stitching_completed(result_path)`: Stitching selesai
- `stitching_error(error_message)`: Error terjadi
- `image_captured(index, image)`: Gambar berhasil diambil

### 2. GridCalculator (GridCalculator.py)
Menghitung grid pergerakan berdasarkan area ROI dan parameter overlap.

**Key Features:**
- Konversi ROI pixel ke koordinat Grbl
- Perhitungan grid points dengan overlap
- Optimasi path untuk meminimalkan waktu pergerakan
- Estimasi waktu total stitching

**Parameters:**
- `camera_fov_width_mm`: Lebar FOV kamera (default: 5.0mm)
- `camera_fov_height_mm`: Tinggi FOV kamera (default: 3.75mm)
- `overlap_percentage`: Persentase overlap antar gambar (5-50%)

### 3. StitchingWorker (StitchingWorker.py)
Worker thread yang menjalankan proses stitching di background.

**Key Features:**
- Menjalankan proses di background thread
- Movement control via Grbl
- Image capture via Toupcam SDK
- Progress reporting
- Error handling

**Process Flow:**
1. Loop untuk setiap grid point
2. Gerakkan Grbl ke posisi
3. Tunggu stabilisasi
4. Ambil gambar
5. Simpan gambar individual
6. Lakukan stitching final

### 4. ImageStitcher (ImageStitcher.py)
Algoritma untuk menggabungkan gambar menggunakan OpenCV.

**Stitching Methods:**
1. **OpenCV Stitcher**: Built-in stitcher OpenCV (recommended)
2. **Manual Stitching**: Feature matching + homography
3. **Simple Grid**: Grid layout tanpa feature matching (fallback)

**Feature Detectors:**
- SIFT (Scale-Invariant Feature Transform)
- ORB (Oriented FAST and Rotated BRIEF)
- AKAZE (Accelerated-KAZE)

**Parameters:**
- `detector_type`: "SIFT", "ORB", atau "AKAZE"
- `matcher_type`: "BF" (Brute Force) atau "FLANN"
- `blend_mode`: "LINEAR" atau "MULTIBAND"

### 5. StitchingUI (StitchingUI.py)
UI components untuk kontrol stitching.

**Components:**
- `StitchingControlWidget`: Widget utama untuk kontrol stitching
- `AdvancedStitchingDialog`: Dialog untuk advanced settings

**Features:**
- Real-time progress monitoring
- Settings adjustment
- Grid information display
- Status logging
- Error reporting

## Usage Example

```python
from Stitching import StitchingController
from PyQt5.QtCore import QRect

# Initialize controller
stitching_controller = StitchingController(
    camera_instance=camera,
    grbl_instance=grbl,
    move_controller=move_controller
)

# Set ROI area (dalam pixel koordinat gambar asli)
roi_rect = QRect(100, 100, 800, 600)
stitching_controller.set_roi_area(roi_rect)

# Set parameters
stitching_controller.set_overlap_percentage(20)  # 20% overlap
stitching_controller.set_capture_delay(1.0)      # 1 second delay

# Connect signals
stitching_controller.stitching_progress.connect(on_progress_updated)
stitching_controller.stitching_completed.connect(on_stitching_completed)
stitching_controller.stitching_error.connect(on_stitching_error)

# Start stitching
stitching_controller.start_stitching("my_session")
```

## Integration dengan UI.py

Untuk mengintegrasikan dengan UI utama:

1. **Import stitching components**:
```python
from Stitching import StitchingController, StitchingControlWidget
```

2. **Initialize di UI.__init__()**:
```python
self.stitching_controller = StitchingController(
    camera_instance=self.camera,
    grbl_instance=self.grbl,
    move_controller=self.roi_label.get_move_controller()
)
```

3. **Add stitching widget ke UI**:
```python
self.stitching_widget = StitchingControlWidget()
self.stitching_widget.set_stitching_controller(self.stitching_controller)
```

4. **Connect ROI selection**:
```python
self.roi_label.roi_selected.connect(self.stitching_controller.set_roi_area)
```

## Configuration

### Camera FOV Settings
Sesuaikan dengan kamera dan lensa yang digunakan:
```python
grid_calculator.set_camera_fov(width_mm=5.0, height_mm=3.75)
```

### Stitching Parameters
- **Overlap**: 10-30% untuk hasil optimal
- **Capture Delay**: 0.5-2.0 detik tergantung stabilitas sistem
- **Feature Detector**: SIFT untuk kualitas terbaik, ORB untuk kecepatan

## Output

Hasil stitching disimpan di:
```
Stitching_Results/
└── session_name/
    ├── image_0000.png    # Individual images
    ├── image_0001.png
    ├── ...
    └── session_name_stitched.jpg  # Final stitched result
```

## Dependencies

- OpenCV (cv2)
- NumPy
- PyQt5
- Toupcam SDK
- Grbl system

## Performance Tips

1. **Gunakan overlap 15-25%** untuk balance antara kualitas dan kecepatan
2. **SIFT detector** untuk kualitas terbaik, **ORB** untuk kecepatan
3. **Capture delay 1-2 detik** untuk stabilitas optimal
4. **Monitor memory usage** untuk stitching area besar

## Troubleshooting

### Common Issues:

1. **"Not enough matches"**: 
   - Kurangi overlap percentage
   - Ganti feature detector
   - Periksa pencahayaan

2. **"Homography estimation failed"**:
   - Tingkatkan overlap
   - Gunakan SIFT detector
   - Periksa kualitas gambar

3. **"Memory error"**:
   - Kurangi ukuran ROI
   - Tingkatkan overlap untuk mengurangi jumlah gambar
   - Gunakan simple grid stitching

4. **Movement errors**:
   - Periksa koneksi Grbl
   - Validasi koordinat grid
   - Tingkatkan capture delay

## Future Enhancements

- [ ] Multi-threading untuk capture paralel
- [ ] Advanced blending algorithms
- [ ] Real-time preview stitching
- [ ] Automatic exposure adjustment
- [ ] Z-stack stitching support
- [ ] GPU acceleration untuk stitching

from PyQt5.QtWidgets import QLabel
from PyQt5.QtCore import Qt, QPoint, QRect, QSize, pyqtSlot, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QPen, QColor

# Import MoveToController untuk fitur Move To
try:
    import importlib.util
    import os

    # Coba beberapa path yang mungkin
    possible_paths = [
        "Move To.py",
        "Software/Camera/Move To.py",
        os.path.join(os.path.dirname(__file__), "Move To.py")
    ]

    MoveToController = None
    for path in possible_paths:
        try:
            if os.path.exists(path):
                spec = importlib.util.spec_from_file_location("Move_To", path)
                move_to_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(move_to_module)
                MoveToController = move_to_module.MoveToController
                print(f"MoveToController berhasil diimport dari: {path}")
                break
        except Exception as e:
            continue

    if MoveToController is None:
        print("Warning: MoveToController tidak dapat diimport dari semua path yang dicoba")

except Exception as e:
    print(f"Warning: Error saat import MoveToController: {e}")
    MoveToController = None


class ROILabel(QLabel):
    display_changed = pyqtSignal(QPixmap)
    pixel_clicked = pyqtSignal(QPoint)

    MODE_ROI_SELECTION = 0
    MODE_PIXEL_COORDS  = 1

    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.original_pixmap = QPixmap() # Menyimpan gambar utuh awal (untuk reset)
        self.display_pixmap = QPixmap()  # Gambar yang selalu terupdate (bisa crop/utuh)

        self.roi_rect = QRect()
        self.begin_point = QPoint()
        self.end_point = QPoint()
        self.is_drawing = False
        
        self.is_cropped = False # Status apakah label ini sedang menampilkan gambar yang di-crop
        # Menyimpan koordinat (x,y,w,h) dari area crop PADA ORIGINAL_PIXMAP.
        # Ini adalah offset yang akan kita tambahkan ke koordinat piksel.
        self.active_crop_offset_on_original = QRect() 
        
        self.setAlignment(Qt.AlignCenter)

        self.mode = self.MODE_ROI_SELECTION 

        self.last_clicked_point_screen = QPoint()
        self.last_clicked_point_pixmap = QPoint()

        # Inisialisasi MoveToController untuk fitur Move To
        self.move_controller = None
        self.grbl_instance = None  # Akan di-set dari luar
        if MoveToController is not None:
            try:
                self.move_controller = MoveToController(
                    center_pixel_x=960,      # Titik tengah resolusi penuh 1920x1080
                    center_pixel_y=540,      # Titik tengah resolusi penuh 1920x1080
                    center_grbl_x=42.228,      # Posisi Grbl untuk titik tengah X
                    center_grbl_y=8.241,      # Posisi Grbl untuk titik tengah Y
                    pixel_to_mm_ratio=0.0278,   # 1 pixel = 0.1 mm
                    grbl_instance=None       # Akan di-set kemudian
                )
                print("MoveToController berhasil diinisialisasi")
            except Exception as e:
                print(f"Error inisialisasi MoveToController: {e}")
                self.move_controller = None

    @pyqtSlot(QPixmap)
    def setPixmap(self, pixmap):
        # Jika pixmap yang masuk adalah original_pixmap (setelah reset), atur is_cropped=False.
        if not pixmap.isNull() and not self.original_pixmap.isNull() and pixmap.size() == self.original_pixmap.size():
            if self.is_cropped: 
                # print(f"ROILabel ({self.objectName()}): Membatalkan crop karena menerima original_pixmap via setPixmap.")
                pass
            self.is_cropped = False
            self.active_crop_offset_on_original = QRect() # Reset offset saat menerima original_pixmap
            self.display_pixmap = pixmap 
            self.update()
            return 
            
        # Jika label ini sedang di-crop secara lokal, abaikan stream yang masuk
        if self.is_cropped:
            return

        # Simpan pixmap pertama yang valid sebagai "master copy" untuk di-reset
        if self.original_pixmap.isNull() and not pixmap.isNull():
            self.original_pixmap = pixmap.copy()
        
        self.display_pixmap = pixmap
        self.update()

    def crop_to_roi(self):
        # Gunakan display_pixmap (frame terbaru) sebagai sumber crop
        source_pixmap = self.display_pixmap if not self.display_pixmap.isNull() else self.original_pixmap
        
        # Hitung koordinat ROI pada source_pixmap
        roi_on_source = self._get_roi_in_display_pixmap_coords(self.roi_rect)
        
        if roi_on_source.isNull() or roi_on_source.isEmpty():
            print("ROI tidak valid untuk di-crop.")
            return
        # 🔹 Simpan offset crop terhadap original_pixmap
        self.active_crop_offset_on_original = self._get_roi_in_original_pixmap_coords(self.roi_rect)
        # Potong dari source_pixmap (frame terbaru)
        cropped = source_pixmap.copy(roi_on_source)
        
        self.display_pixmap = cropped
        self.is_cropped = True
        self.display_changed.emit(cropped)
        self.clear_roi()

        print("\n" + "="*50)
        print(f"ROILabel ({self.objectName()}): Crop Berhasil!")
        print(f"  ROI Layar (Widget): {self.roi_rect.x()},{self.roi_rect.y()} -> {self.roi_rect.x()+self.roi_rect.width()},{self.roi_rect.y()+self.roi_rect.height()} ({self.roi_rect.width()}x{self.roi_rect.height()} px)")
        print(f"  ROI Absolut (Original Pixmap): {self.active_crop_offset_on_original.x()},{self.active_crop_offset_on_original.y()} -> {self.active_crop_offset_on_original.x()+self.active_crop_offset_on_original.width()},{self.active_crop_offset_on_original.y()+self.active_crop_offset_on_original.height()} ({self.active_crop_offset_on_original.width()}x{self.active_crop_offset_on_original.height()} px)")
        print(f"  Gambar Asli (Full): {self.original_pixmap.width()}x{self.original_pixmap.height()} px")
        print(f"  Gambar yang Ditampilkan (Cropped): {self.display_pixmap.width()}x{self.display_pixmap.height()} px")
        print("="*50 + "\n")
        
    def reset_view(self):
        if self.is_cropped:
            self.is_cropped = False
            self.active_crop_offset_on_original = QRect()
            # Jangan set display_pixmap ke original_pixmap lama
            # Biarkan setPixmap() dari stream mengupdate display_pixmap
            self.display_changed.emit(QPixmap())  # Emit pixmap kosong sebagai sinyal reset
        self.clear_roi()  # Clear ROI setelah reset

    def clear_roi(self):
        self.roi_rect = QRect()
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.fillRect(self.rect(), QColor(45, 45, 45))

        if not self.display_pixmap.isNull():
            scaled_pixmap = self.display_pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            x = (self.width() - scaled_pixmap.width()) / 2
            y = (self.height() - scaled_pixmap.height()) / 2
            target_rect = QRect(int(x), int(y), scaled_pixmap.width(), scaled_pixmap.height())
            
            painter.drawPixmap(target_rect, scaled_pixmap)
        
        if self.mode == self.MODE_ROI_SELECTION:
            roi_to_draw = QRect(self.begin_point, self.end_point).normalized() if self.is_drawing else self.roi_rect
            if not roi_to_draw.isNull() and roi_to_draw.isValid():
                pen = QPen(QColor(0, 255, 0, 200) if self.is_drawing else QColor(255, 0, 0, 200), 2)
                pen.setStyle(Qt.DashLine if self.is_drawing else Qt.SolidLine)
                painter.setPen(pen)
                painter.drawRect(roi_to_draw)
            
    # Metode helper untuk mendapatkan geometri pixmap yang sedang ditampilkan di widget
    def _get_displayed_pixmap_geometry(self):
        if self.display_pixmap.isNull(): return QRect()
        scaled_size = self.display_pixmap.size().scaled(self.size(), Qt.KeepAspectRatio)
        x = (self.width() - scaled_size.width()) / 2
        y = (self.height() - scaled_size.height()) / 2
        return QRect(int(x), int(y), scaled_size.width(), scaled_size.height())

    # Metode helper baru untuk mendapatkan geometri original_pixmap saat ditampilkan di widget
    def _get_displayed_pixmap_geometry_for_original(self):
        if self.original_pixmap.isNull(): return QRect()
        scaled_size = self.original_pixmap.size().scaled(self.size(), Qt.KeepAspectRatio)
        x = (self.width() - scaled_size.width()) / 2
        y = (self.height() - scaled_size.height()) / 2
        return QRect(int(x), int(y), scaled_size.width(), scaled_size.height())

    # Metode untuk mendapatkan ROI dari layar ke koordinat ORIGINAL_PIXMAP
    def _get_roi_in_original_pixmap_coords(self, roi_screen_rect: QRect):
        if not roi_screen_rect.isValid() or self.original_pixmap.isNull(): return QRect()
        
        # Referensikan geometri original_pixmap di layar
        displayed_original_geometry = self._get_displayed_pixmap_geometry_for_original()
        
        if displayed_original_geometry.width() == 0 or displayed_original_geometry.height() == 0: return QRect()
        
        x_ratio = self.original_pixmap.width() / displayed_original_geometry.width()
        y_ratio = self.original_pixmap.height() / displayed_original_geometry.height()
        
        relative_roi = roi_screen_rect.translated(-displayed_original_geometry.topLeft())
        
        px1 = max(0, int(relative_roi.x() * x_ratio))
        py1 = max(0, int(relative_roi.y() * y_ratio))
        px2 = int((relative_roi.x() + relative_roi.width()) * x_ratio)
        py2 = int((relative_roi.y() + relative_roi.height()) * y_ratio)
        
        px2 = min(px2, self.original_pixmap.width())
        py2 = min(py2, self.original_pixmap.height())

        return QRect(QPoint(px1, py1), QPoint(px2, py2)).normalized()

    # Metode untuk mendapatkan ROI dari layar ke koordinat DISPLAY_PIXMAP
    def _get_roi_in_display_pixmap_coords(self, roi_screen_rect: QRect):
        if not roi_screen_rect.isValid() or self.display_pixmap.isNull(): return QRect()
        
        displayed_geometry = self._get_displayed_pixmap_geometry()
        
        if displayed_geometry.width() == 0 or displayed_geometry.height() == 0: return QRect()
        
        x_ratio = self.display_pixmap.width() / displayed_geometry.width()
        y_ratio = self.display_pixmap.height() / displayed_geometry.height()
        
        relative_roi = roi_screen_rect.translated(-displayed_geometry.topLeft())
        
        px1 = max(0, int(relative_roi.x() * x_ratio))
        py1 = max(0, int(relative_roi.y() * y_ratio))
        px2 = int((relative_roi.x() + relative_roi.width()) * x_ratio)
        py2 = int((relative_roi.y() + relative_roi.height()) * y_ratio)
        
        px2 = min(px2, self.display_pixmap.width())
        py2 = min(py2, self.display_pixmap.height())

        return QRect(QPoint(px1, py1), QPoint(px2, py2)).normalized()

    def get_roi_size(self):
        if self.roi_rect.isNull() or not self.roi_rect.isValid():
            return None

        screen_size = {
            'width': self.roi_rect.width(),
            'height': self.roi_rect.height()
        }

        # Dapatkan ROI size pada original_pixmap (absolut)
        pixmap_roi_rect = self._get_roi_in_original_pixmap_coords(self.roi_rect)
        pixmap_size = None
        if pixmap_roi_rect:
            pixmap_size = {
                'width': pixmap_roi_rect.width(),
                'height': pixmap_roi_rect.height()
            }
            
        return {
            'screen_size': screen_size,
            'pixmap_size': pixmap_size
        }
    
    def _get_pixel_coords_from_screen_point(self, screen_point: QPoint):
        # Konversi titik klik layar ke koordinat relatif pada display_pixmap
        point_on_display_pixmap = self._get_roi_in_display_pixmap_coords(QRect(screen_point, QSize(1,1))).topLeft()

        if point_on_display_pixmap.isNull() or self.display_pixmap.isNull() or not self._get_displayed_pixmap_geometry().contains(screen_point):
            print("ROILabel:_get_pixel_coords_from_screen_point: Konversi ke display_pixmap gagal atau klik di luar area gambar.")
            return None
        
        # Jika tampilan saat ini di-crop, tambahkan offset dari active_crop_offset_on_original
        if self.is_cropped and not self.active_crop_offset_on_original.isNull():
            # Tambahkan koordinat point_on_display_pixmap ke top-left dari active_crop_offset_on_original
            pixel_coords_on_original = self.active_crop_offset_on_original.topLeft() + point_on_display_pixmap
            
            # Pastikan koordinat hasil akhir berada dalam batas active_crop_offset_on_original
            clamped_x = max(self.active_crop_offset_on_original.left(), min(pixel_coords_on_original.x(), self.active_crop_offset_on_original.right() - 1))
            clamped_y = max(self.active_crop_offset_on_original.top(), min(pixel_coords_on_original.y(), self.active_crop_offset_on_original.bottom() - 1))
            
            return QPoint(clamped_x, clamped_y)
        else:
            # Jika tidak di-crop, koordinat sudah relatif terhadap original_pixmap (dari _get_roi_in_original_pixmap_coords)
            # Karena _get_roi_in_display_pixmap_coords sekarang digunakan, kita perlu mengkonversi ke original_pixmap.
            # Ini artinya harus mengkonversi dari display_pixmap ke original_pixmap
            print(self.active_crop_offset_on_original)
            print(self.is_cropped)
            return self._get_roi_in_original_pixmap_coords(QRect(screen_point, QSize(1,1))).topLeft()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if not self._get_displayed_pixmap_geometry().contains(event.pos()):
                return

            if self.mode == self.MODE_ROI_SELECTION:
                print("ROI mode")
                self.is_drawing = True
                self.begin_point = event.pos()
                self.end_point = event.pos()
                self.update()
            elif self.mode == self.MODE_PIXEL_COORDS:
                print("pixel mode")
                pixel_coords = self._get_pixel_coords_from_screen_point(event.pos())
                if pixel_coords:
                    self.last_clicked_point_screen = event.pos()
                    self.last_clicked_point_pixmap = pixel_coords
                    self.pixel_clicked.emit(pixel_coords)
                    self.update()
                    print(f"Koordinat layar (relatif): {event.pos()}")
                    print(f"Koordinat piksel (absolut): {pixel_coords}")

                    # Integrasi Move To - konversi pixel ke Grbl dan kirim command
                    self._execute_move_to(pixel_coords.x(), pixel_coords.y())
                else:
                    print("pixel mode: Gagal mendapatkan koordinat piksel.")

    def mouseMoveEvent(self, event):
        if self.mode == self.MODE_ROI_SELECTION and self.is_drawing:
            self.end_point = event.pos()
            self.update()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.mode == self.MODE_ROI_SELECTION and self.is_drawing:
                self.is_drawing = False
                self.roi_rect = QRect(self.begin_point, self.end_point).normalized()
                if self.roi_rect.width() < 5 or self.roi_rect.height() < 5:
                    self.roi_rect = QRect()
                self.update()

                # --- TAMBAHAN LOG UNTUK ROI ---
                if not self.roi_rect.isNull() and self.move_controller:
                    # Konversi koordinat layar ROI ke koordinat piksel absolut
                    roi_start_pixel = self._get_pixel_coords_from_screen_point(self.roi_rect.topLeft())
                    roi_end_pixel = self._get_pixel_coords_from_screen_point(self.roi_rect.bottomRight())

                    if roi_start_pixel and roi_end_pixel:
                        # Konversi koordinat piksel ke GRBL
                        grbl_start = self.move_controller.pixel_to_grbl_coordinates(roi_start_pixel.x(), roi_start_pixel.y())
                        grbl_end = self.move_controller.pixel_to_grbl_coordinates(roi_end_pixel.x(), roi_end_pixel.y())

                        print("\n" + "="*60)
                        print(" " * 20 + "ROI SELECTION LOG")
                        print("="*60)
                        print(f"  Area (Pixel Absolut):")
                        print(f"    - Titik Awal (Top-Left):   ({roi_start_pixel.x()}, {roi_start_pixel.y()}) px")
                        print(f"    - Titik Akhir (Bottom-Right): ({roi_end_pixel.x()}, {roi_end_pixel.y()}) px")
                        print(f"    - Ukuran (Lebar x Tinggi): {roi_end_pixel.x() - roi_start_pixel.x()} x {roi_end_pixel.y() - roi_start_pixel.y()} px")
                        print(f"  Area (GRBL):")
                        print(f"    - Posisi Awal (Top-Left):   X={grbl_start[0]:.3f}, Y={grbl_start[1]:.3f} mm")
                        print(f"    - Posisi Akhir (Bottom-Right): X={grbl_end[0]:.3f}, Y={grbl_end[1]:.3f} mm")
                        print("="*60 + "\n")

    @pyqtSlot(int)
    def set_mode(self, mode):
        if mode not in [self.MODE_ROI_SELECTION, self.MODE_PIXEL_COORDS]:
            print(f"[WARNING] Mode {mode} tidak valid untuk ROILabel.")
            return

        self.mode = mode
        self.clear_roi()
        self.last_clicked_point_screen = QPoint()
        self.last_clicked_point_pixmap = QPoint()
        self.update()
        print(f"ROILabel mode diubah ke: {'ROI Selection' if self.mode == self.MODE_ROI_SELECTION else 'Pixel Coordinates'}")

    def _execute_move_to(self, pixel_x: int, pixel_y: int):
        """
        Mengeksekusi fitur Move To: konversi pixel ke koordinat Grbl dan kirim command

        Args:
            pixel_x: Koordinat X dalam pixel dari gambar asli (0-1920)
            pixel_y: Koordinat Y dalam pixel dari gambar asli (0-1080)
        """
        if self.move_controller is None:
            print("Move To: MoveToController tidak tersedia")
            return

        try:
            # Konversi pixel ke koordinat Grbl
            grbl_x, grbl_y = self.move_controller.pixel_to_grbl_coordinates(pixel_x, pixel_y)

            # Debug print untuk menampilkan informasi konversi
            print("=" * 50)
            print("MOVE TO DEBUG INFO:")
            print(f"Pixel coordinates (original): ({pixel_x}, {pixel_y})")
            print(f"Center pixel: ({self.move_controller.center_pixel_x}, {self.move_controller.center_pixel_y})")
            print(f"Center Grbl: ({self.move_controller.center_grbl_x}, {self.move_controller.center_grbl_y})")
            print(f"Pixel to mm ratio: {self.move_controller.pixel_to_mm_ratio}")
            print(f"Grbl coordinates: ({grbl_x:.3f}, {grbl_y:.3f})")

            # Buat G-code command
            gcode_command = f"G0 X{grbl_x:.3f} Y{grbl_y:.3f} F1000"
            print(f"G-code command: {gcode_command}")
            print("=" * 50)

            # Pastikan Grbl instance sudah di-set
            if self.move_controller.grbl_instance is None:
                print("Move To: Grbl instance belum di-set. Command tidak dikirim.")
                return

            # Kirim command ke Grbl melalui instance yang sudah ada
            success = self.move_controller.move_to_pixel(pixel_x, pixel_y)

            if success:
                print(f"Move To: Berhasil bergerak ke pixel ({pixel_x}, {pixel_y}) -> Grbl ({grbl_x:.3f}, {grbl_y:.3f})")
            else:
                print(f"Move To: Gagal bergerak ke pixel ({pixel_x}, {pixel_y})")

        except Exception as e:
            print(f"Move To Error: {e}")

    def set_grbl_instance(self, grbl_instance):
        """
        Set instance Grbl yang akan digunakan untuk Move To

        Args:
            grbl_instance: Instance dari class Grbl yang sudah ada di sistem
        """
        self.grbl_instance = grbl_instance
        if self.move_controller is not None:
            self.move_controller.set_grbl_instance(grbl_instance)
            print("ROILabel: Grbl instance berhasil di-set untuk MoveToController")

    def get_move_controller(self):
        """
        Mendapatkan instance MoveToController

        Returns:
            MoveToController instance atau None
        """
        return self.move_controller

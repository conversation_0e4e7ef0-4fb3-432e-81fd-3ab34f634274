#!/usr/bin/env python3
"""
Test script untuk memverifikasi fungsionalitas ROI yang telah diperbaiki.

Perubahan yang telah dilakukan:
1. lbl_video sekarang adalah QLabel biasa (tanpa ROI)
2. lbl_live_preview_popup adalah satu-satunya ROILabel
3. Semua fungsi ROI (crop, reset, mapping AF) selalu menargetkan lbl_live_preview_popup
4. Swap posisi fisik antara kedua label saat diklik
5. ROI controls hanya aktif ketika lbl_live_preview_popup di posisi utama

Fitur yang harus ditest:
- Swap posisi dengan klik pada popup
- ROI selection hanya pada lbl_live_preview_popup
- Crop ROI berfungsi
- Reset ROI berfungsi
- Mapping AF berfungsi dengan ROI yang dipilih
- Move To berfungsi
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test tanpa menjalankan UI penuh untuk menghindari dependency issues
def test_code_structure():
    """Test struktur kode tanpa menjalankan UI"""
    print("="*60)
    print("TESTING ROI CODE STRUCTURE")
    print("="*60)

    # Test import dan struktur kode
    try:
        # Test import ROILabel
        from Camera.RoiLabel import ROILabel
        print("✓ ROILabel import successful")

        # Test import QLabel
        from PyQt5.QtWidgets import QLabel
        print("✓ QLabel import successful")

        # Test baca file UI.py untuk verifikasi perubahan
        with open('Software/UI.py', 'r', encoding='utf-8') as f:
            ui_content = f.read()

        # Check perubahan struktur label
        if 'self.lbl_video = QLabel(video_container)' in ui_content:
            print("✓ lbl_video is now QLabel (not ROILabel)")
        else:
            print("✗ lbl_video should be QLabel")

        if 'self.lbl_live_preview_popup = ROILabel(video_container)' in ui_content:
            print("✓ lbl_live_preview_popup is ROILabel")
        else:
            print("✗ lbl_live_preview_popup should be ROILabel")

        # Check fungsi swap (seharusnya tidak ada _swap_label_positions)
        if '_swap_label_positions' not in ui_content:
            print("✓ No _swap_label_positions function (correct - swap only changes streams)")
        else:
            print("✗ _swap_label_positions function should not exist")

        # Check inisialisasi ROI controls
        if '_initialize_roi_controls' in ui_content:
            print("✓ _initialize_roi_controls function exists")
        else:
            print("✗ _initialize_roi_controls function missing")

        # Check konsistensi fungsi ROI
        crop_roi_checks = [
            'self.lbl_live_preview_popup.crop_to_roi()' in ui_content,
            'self.lbl_live_preview_popup.reset_view()' in ui_content,
            'self.lbl_live_preview_popup.roi_rect.isValid()' in ui_content
        ]

        if all(crop_roi_checks):
            print("✓ ROI functions consistently target lbl_live_preview_popup")
        else:
            print("✗ ROI functions not consistently targeting lbl_live_preview_popup")

        print("\n" + "="*60)
        print("CODE STRUCTURE TEST COMPLETED")
        print("="*60)

        return True

    except Exception as e:
        print(f"✗ Error during testing: {e}")
        return False

def main():
    """Main function untuk menjalankan test"""
    print("Starting ROI functionality tests...")

    # Test struktur kode
    success = test_code_structure()

    if success:
        print("\n🎉 All code structure tests passed!")
        print("\nTo test the full functionality:")
        print("1. Run the main application: python Software/UI.py")
        print("2. Follow the manual testing instructions in ROI_IMPROVEMENTS_DOCUMENTATION.md")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

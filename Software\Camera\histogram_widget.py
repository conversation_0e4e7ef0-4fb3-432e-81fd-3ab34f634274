# File: histogram_widget.py

from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QPainter, QColor, QPen, QPainterPath
from PyQt5.QtCore import pyqtSlot, Qt
import math

class HistogramWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(120)
        
        self.histogram_data = []
        self.channel_mode = 'rgb'
        self.scale_mode = 'linear'
        
        self.black_level_pos = 0
        self.white_level_pos = 255
        
        # Warna untuk latar putih
        self.pen_r = QPen(QColor(220, 30, 30, 200), 1.5)
        self.pen_g = QPen(QColor(30, 180, 30, 200), 1.5)
        self.pen_b = QPen(QColor(30, 30, 220, 200), 1.5)
        self.pen_lum = QPen(QColor(Qt.black), 1)
        self.level_pen = QPen(QColor(0, 200, 200, 150), 1.5, Qt.DashLine)
        self.background_color = QColor(Qt.white)

    @pyqtSlot(object)
    def update_histogram(self, data):
        if isinstance(data, list):
            self.histogram_data = data
            self.update()

    @pyqtSlot(str)
    def set_channel_mode(self, mode):
        self.channel_mode = mode.lower()
        self.update()

    @pyqtSlot(str)
    def set_scale_mode(self, mode):
        self.scale_mode = mode.lower()
        self.update()

    @pyqtSlot(int, int)
    def set_level_lines(self, black, white):
        self.black_level_pos = black
        self.white_level_pos = white
        self.update()

    def _draw_channel(self, painter, data, pen, max_val):
        if not data or max_val == 0:
            return
            
        painter.setPen(pen)
        w, h = self.width(), self.height()
        log_max = math.log(max_val + 1)
        path = QPainterPath()
        
        y_start = 0
        if self.scale_mode == 'logarithmic' and data[0] > 0:
            y_start = int((math.log(data[0] + 1) / log_max) * (h - 10))
        else:
            y_start = int((data[0] / max_val) * (h - 10))
        path.moveTo(0, h - y_start)

        for i, val in enumerate(data[1:], 1):
            x = int(i * w / 255)
            y = 0
            if self.scale_mode == 'logarithmic' and val > 0:
                y = int((math.log(val + 1) / log_max) * (h - 10))
            else:
                y = int((val / max_val) * (h - 10))
            path.lineTo(x, h - y)

        painter.drawPath(path)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # >>> BARIS KUNCI: INI YANG MEMBERSIHKAN TAMPILAN SEBELUM MENGGAMBAR <<<
        painter.fillRect(self.rect(), self.background_color)

        # --- Bagian menggambar grafik (tidak berubah) ---
        if not self.histogram_data or len(self.histogram_data) not in [256, 768]:
            return
            
        r_data, g_data, b_data, lum_data = [], [], [], []
        if len(self.histogram_data) == 768:
            r_data = self.histogram_data[0:256]; g_data = self.histogram_data[256:512]; b_data = self.histogram_data[512:768]
            lum_data = [(r + g + b) / 3 for r, g, b in zip(r_data, g_data, b_data)]
        else:
            lum_data = self.histogram_data
        
        visible_data = []
        if self.channel_mode in ['rgb', 'luminance']: visible_data.extend(lum_data)
        if self.channel_mode in ['rgb', 'red']: visible_data.extend(r_data)
        if self.channel_mode in ['rgb', 'green']: visible_data.extend(g_data)
        if self.channel_mode in ['rgb', 'blue']: visible_data.extend(b_data)
        max_val = max(visible_data) if visible_data else 1

        if self.channel_mode in ['rgb', 'luminance'] and lum_data: self._draw_channel(painter, lum_data, self.pen_lum, max_val)
        if self.channel_mode in ['rgb', 'red'] and r_data: self._draw_channel(painter, r_data, self.pen_r, max_val)
        if self.channel_mode in ['rgb', 'green'] and g_data: self._draw_channel(painter, g_data, self.pen_g, max_val)
        if self.channel_mode in ['rgb', 'blue'] and b_data: self._draw_channel(painter, b_data, self.pen_b, max_val)

        # --- Bagian menggambar garis Levels (tidak berubah) ---
        w, h = self.width(), self.height()
        painter.setPen(self.level_pen)
        x_black = int(self.black_level_pos * w / 255); painter.drawLine(x_black, 0, x_black, h)
        x_white = int(self.white_level_pos * w / 255); painter.drawLine(x_white, 0, x_white, h)
"""
Stitching.py - Main Controller untuk Sistem Stitching
Menggabungkan beberapa gambar dari area ROI yang dipilih

Workflow:
1. Terima area ROI dari user
2. Hitung grid pergerakan dalam area ROI
3. Gerakkan Grbl ke setiap titik grid
4. Ambil gambar dari Toupcam SDK di setiap titik
5. Stitch semua gambar menjadi satu gambar besar
6. Simpan dan tampilkan hasil
"""

from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QRect, QPoint
from PyQt5.QtGui import QPixmap
from typing import List, Tuple, Optional, Dict
import os
import time
from datetime import datetime
from configuration import config

class StitchingController(QObject):
    # Signals
    stitching_started = pyqtSignal()
    stitching_progress = pyqtSignal(int, int, str)  # current, total, status
    stitching_completed = pyqtSignal(str)  # result_path
    stitching_error = pyqtSignal(str)  # error_message
    image_captured = pyqtSignal(int, QPixmap)  # index, image
    
    def __init__(self, camera_instance=None, grbl_instance=None, move_controller=None):
        """
        Inisialisasi Stitching Controller
        
        Args:
            camera_instance: Instance dari Main_Camera (Toupcam SDK)
            grbl_instance: Instance dari Grbl controller
            move_controller: Instance dari MoveToController untuk konversi koordinat
        """
        super().__init__()
        
        self.camera_instance = camera_instance
        self.grbl_instance = grbl_instance
        self.move_controller = move_controller
        
        # Stitching parameters (soft-coded from configuration)
        self.roi_area = None  # QRect - area ROI yang dipilih (dalam pixel)
        self.overlap_percentage = int(config.get("stitching.overlap_percentage", 20))  # Overlap antar gambar (%)
        self.grid_points = []  # List koordinat Grbl untuk setiap titik grid
        self.captured_images = []  # List gambar yang sudah diambil
        self.capture_delay = float(config.get("stitching.capture_delay_s", 1.0))  # Delay setelah movement sebelum capture (detik)
        
        # Status
        self.is_stitching = False
        self.current_step = 0
        self.total_steps = 0
        
        # Output settings
        self.output_directory = config.get("stitching.output_directory", "Stitching_Results")
        self.session_name = ""
        
        # Import dependencies
        try:
            from .GridCalculator import GridCalculator
            from .ImageStitcher import ImageStitcher
            self.grid_calculator = GridCalculator()
            self.image_stitcher = ImageStitcher()
        except ImportError as e:
            print(f"Stitching: Error importing dependencies: {e}")
            self.grid_calculator = None
            self.image_stitcher = None
    
    def set_camera_instance(self, camera_instance):
        """Set instance kamera Toupcam"""
        self.camera_instance = camera_instance
        print("Stitching: Camera instance berhasil di-set")
    
    def set_grbl_instance(self, grbl_instance):
        """Set instance Grbl controller"""
        self.grbl_instance = grbl_instance
        print("Stitching: Grbl instance berhasil di-set")
    
    def set_move_controller(self, move_controller):
        """Set instance MoveToController untuk konversi koordinat"""
        self.move_controller = move_controller
        print("Stitching: MoveToController instance berhasil di-set")
    
    def set_roi_area(self, roi_rect: QRect):
        """
        Set area ROI yang akan di-stitch
        
        Args:
            roi_rect: QRect area ROI dalam koordinat pixel (gambar asli)
        """
        self.roi_area = roi_rect
        print(f"Stitching: ROI area di-set: {roi_rect}")
        
        # Hitung grid points berdasarkan ROI
        if self.grid_calculator and self.move_controller:
            self.grid_points = self.grid_calculator.calculate_grid(
                roi_rect,
                self.move_controller,
                self.overlap_percentage
            )
            self.total_steps = len(self.grid_points)

            # Dapatkan grid info untuk stitching
            grid_info = self.grid_calculator.get_last_grid_info()
            if grid_info and self.image_stitcher:
                self.image_stitcher.set_grid_parameters(
                    grid_info['grid_cols'],
                    grid_info['grid_rows'],
                    self.overlap_percentage
                )

            print(f"Stitching: Grid calculated - {self.total_steps} points")
    
    def set_overlap_percentage(self, overlap: int):
        """Set persentase overlap antar gambar"""
        self.overlap_percentage = max(0, min(50, overlap))  # Limit 0-50%
        print(f"Stitching: Overlap percentage set to {self.overlap_percentage}%")
    
    def set_capture_delay(self, delay: float):
        """Set delay setelah movement sebelum capture"""
        self.capture_delay = max(0.1, delay)
        print(f"Stitching: Capture delay set to {self.capture_delay}s")
    
    def start_stitching(self, session_name: str = ""):
        """
        Mulai proses stitching
        
        Args:
            session_name: Nama session untuk output file
        """
        if self.is_stitching:
            print("Stitching: Proses stitching sudah berjalan")
            return False
        
        # Validasi prerequisites
        if not self._validate_prerequisites():
            return False
        
        # Setup session
        self.session_name = session_name or f"stitch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.captured_images = []
        self.current_step = 0
        self.is_stitching = True
        
        # Create output directory
        self._create_output_directory()
        
        print(f"Stitching: Memulai proses stitching - {self.total_steps} titik")
        self.stitching_started.emit()
        
        # Start stitching process
        self._start_stitching_process()
        
        return True
    
    def stop_stitching(self):
        """Stop proses stitching"""
        if self.is_stitching:
            self.is_stitching = False
            print("Stitching: Proses stitching dihentikan")
    
    def _validate_prerequisites(self) -> bool:
        """Validasi semua prerequisite untuk stitching"""
        if not self.camera_instance:
            self.stitching_error.emit("Camera instance tidak tersedia")
            return False
        
        if not self.grbl_instance:
            self.stitching_error.emit("Grbl instance tidak tersedia")
            return False
        
        if not self.move_controller:
            self.stitching_error.emit("MoveToController tidak tersedia")
            return False
        
        if not self.roi_area:
            self.stitching_error.emit("Area ROI belum dipilih")
            return False
        
        if not self.grid_points:
            self.stitching_error.emit("Grid points belum dihitung")
            return False
        
        if not self.grid_calculator or not self.image_stitcher:
            self.stitching_error.emit("Stitching dependencies tidak tersedia")
            return False
        
        return True
    
    def _create_output_directory(self):
        """Buat direktori output untuk hasil stitching"""
        try:
            if not os.path.exists(self.output_directory):
                os.makedirs(self.output_directory)
            
            session_dir = os.path.join(self.output_directory, self.session_name)
            if not os.path.exists(session_dir):
                os.makedirs(session_dir)
                
            print(f"Stitching: Output directory created: {session_dir}")
        except Exception as e:
            print(f"Stitching: Error creating output directory: {e}")
    
    def _start_stitching_process(self):
        """Mulai proses stitching secara asynchronous"""
        # Import dan start worker thread
        try:
            from .StitchingWorker import StitchingWorker
            from PyQt5.QtCore import QThread
            
            # Create worker thread
            self.stitching_thread = QThread()
            self.stitching_worker = StitchingWorker(
                self.camera_instance,
                self.grbl_instance,
                self.move_controller,
                self.grid_points,
                self.capture_delay,
                self.session_name,
                self.output_directory
            )
            
            # Move worker to thread
            self.stitching_worker.moveToThread(self.stitching_thread)
            
            # Connect signals
            self.stitching_thread.started.connect(self.stitching_worker.run_stitching)
            self.stitching_worker.progress_updated.connect(self._on_progress_updated)
            self.stitching_worker.image_captured.connect(self._on_image_captured)
            self.stitching_worker.stitching_completed.connect(self._on_stitching_completed)
            self.stitching_worker.stitching_error.connect(self._on_stitching_error)
            self.stitching_worker.finished.connect(self.stitching_thread.quit)
            self.stitching_thread.finished.connect(self.stitching_thread.deleteLater)
            
            # Start thread
            self.stitching_thread.start()
            
        except ImportError as e:
            self.stitching_error.emit(f"Error importing StitchingWorker: {e}")
    
    @pyqtSlot(int, int, str)
    def _on_progress_updated(self, current: int, total: int, status: str):
        """Handle progress update dari worker"""
        self.current_step = current
        self.stitching_progress.emit(current, total, status)
    
    @pyqtSlot(int, QPixmap)
    def _on_image_captured(self, index: int, image: QPixmap):
        """Handle image captured dari worker"""
        self.captured_images.append(image)
        self.image_captured.emit(index, image)
    
    @pyqtSlot(str)
    def _on_stitching_completed(self, result_path: str):
        """Handle stitching completed"""
        self.is_stitching = False
        self.stitching_completed.emit(result_path)
        print(f"Stitching: Completed - Result saved to: {result_path}")
    
    @pyqtSlot(str)
    def _on_stitching_error(self, error_message: str):
        """Handle stitching error"""
        self.is_stitching = False
        self.stitching_error.emit(error_message)
        print(f"Stitching: Error - {error_message}")
    
    def get_stitching_info(self) -> Dict:
        """Mendapatkan informasi status stitching"""
        return {
            'is_stitching': self.is_stitching,
            'current_step': self.current_step,
            'total_steps': self.total_steps,
            'roi_area': self.roi_area,
            'overlap_percentage': self.overlap_percentage,
            'capture_delay': self.capture_delay,
            'session_name': self.session_name,
            'grid_points_count': len(self.grid_points),
            'captured_images_count': len(self.captured_images)
        }

# Contoh penggunaan
if __name__ == "__main__":
    print("Stitching Controller - Main module")
    print("Gunakan dari UI.py atau aplikasi utama")

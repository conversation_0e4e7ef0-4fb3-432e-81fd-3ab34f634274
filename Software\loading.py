#File: loading.py

from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

'''
class LoadingSplash untuk UI loadingSplash
'''
class LoadingSplash(QWidget):
    def __init__(self):
        super().__init__()
        self.setFixedSize(300, 150)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setStyleSheet("background-color: white;")

        # Teks loading
        self.label = QLabel("Loading...", self)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setFont(QFont("Arial", 16))
        self.label.setStyleSheet("color: black;")

        # Layout
        layout = QVBoxLayout(self)
        layout.addStretch()
        layout.addWidget(self.label)
        layout.addStretch()
